"""
MySQL Database System with 3NF Design
=====================================

This module provides MySQL database integration following Third Normal Form (3NF)
for user analytics, support tickets, and chat persistence.
"""

import mysql.connector
from mysql.connector import Error
import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import uuid
import json
from typing import Dict, List, Optional, Any
import os
from contextlib import contextmanager

class MySQLDatabase:
    """MySQL database manager with 3NF design"""
    
    def __init__(self, config: Dict[str, str] = None):
        """Initialize MySQL connection with config"""
        self.config = config or self._get_default_config()
        self.connection = None
    
    def _get_default_config(self) -> Dict[str, str]:
        """Get default MySQL configuration"""
        return {
            'host': os.getenv('MYSQL_HOST', 'localhost'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'database': os.getenv('MYSQL_DATABASE', 'streamlit_analytics'),
            'user': os.getenv('MYSQL_USER', 'root'),
            'password': os.getenv('MYSQL_PASSWORD', ''),
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci'
        }
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        connection = None
        try:
            connection = mysql.connector.connect(**self.config)
            yield connection
        except Error as e:
            st.error(f"Database connection error: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def initialize_database(self):
        """Initialize database and create tables following 3NF"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Create database if not exists
                cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.config['database']} "
                             f"CHARACTER SET {self.config['charset']} "
                             f"COLLATE {self.config['collation']}")
                cursor.execute(f"USE {self.config['database']}")
                
                # 1NF: Users table (atomic values, unique rows)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS users (
                        user_id VARCHAR(50) PRIMARY KEY,
                        username VARCHAR(100) NOT NULL UNIQUE,
                        email VARCHAR(255),
                        first_name VARCHAR(100),
                        last_name VARCHAR(100),
                        role ENUM('user', 'admin') DEFAULT 'user',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT TRUE,
                        INDEX idx_username (username),
                        INDEX idx_email (email),
                        INDEX idx_role (role)
                    ) ENGINE=InnoDB
                """)
                
                # 1NF: Browser types table (separate entity)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS browser_types (
                        browser_id INT AUTO_INCREMENT PRIMARY KEY,
                        browser_name VARCHAR(100) NOT NULL UNIQUE,
                        browser_version VARCHAR(50),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB
                """)
                
                # 1NF: Device types table (separate entity)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS device_types (
                        device_id INT AUTO_INCREMENT PRIMARY KEY,
                        device_name VARCHAR(100) NOT NULL UNIQUE,
                        device_category ENUM('desktop', 'mobile', 'tablet', 'other') DEFAULT 'other',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB
                """)
                
                # 2NF: User sessions table (no partial dependencies)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS user_sessions (
                        session_id VARCHAR(50) PRIMARY KEY,
                        user_id VARCHAR(50) NOT NULL,
                        browser_id INT,
                        device_id INT,
                        ip_address VARCHAR(45),
                        user_agent TEXT,
                        start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        end_time TIMESTAMP NULL,
                        duration_seconds INT DEFAULT 0,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                        FOREIGN KEY (browser_id) REFERENCES browser_types(browser_id),
                        FOREIGN KEY (device_id) REFERENCES device_types(device_id),
                        INDEX idx_user_id (user_id),
                        INDEX idx_start_time (start_time),
                        INDEX idx_is_active (is_active)
                    ) ENGINE=InnoDB
                """)
                
                # 1NF: Pages table (separate entity)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS pages (
                        page_id INT AUTO_INCREMENT PRIMARY KEY,
                        page_name VARCHAR(200) NOT NULL UNIQUE,
                        page_category VARCHAR(100),
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB
                """)
                
                # 2NF: Page visits table (no partial dependencies)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS page_visits (
                        visit_id INT AUTO_INCREMENT PRIMARY KEY,
                        session_id VARCHAR(50) NOT NULL,
                        page_id INT NOT NULL,
                        visit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        duration_seconds INT DEFAULT 0,
                        interactions_count INT DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES user_sessions(session_id) ON DELETE CASCADE,
                        FOREIGN KEY (page_id) REFERENCES pages(page_id),
                        INDEX idx_session_id (session_id),
                        INDEX idx_page_id (page_id),
                        INDEX idx_visit_time (visit_time)
                    ) ENGINE=InnoDB
                """)
                
                # 1NF: Interaction types table (separate entity)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS interaction_types (
                        interaction_type_id INT AUTO_INCREMENT PRIMARY KEY,
                        type_name VARCHAR(100) NOT NULL UNIQUE,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB
                """)
                
                # 3NF: User interactions table (no transitive dependencies)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS user_interactions (
                        interaction_id INT AUTO_INCREMENT PRIMARY KEY,
                        session_id VARCHAR(50) NOT NULL,
                        page_id INT NOT NULL,
                        interaction_type_id INT NOT NULL,
                        element_name VARCHAR(200),
                        interaction_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        details JSON,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES user_sessions(session_id) ON DELETE CASCADE,
                        FOREIGN KEY (page_id) REFERENCES pages(page_id),
                        FOREIGN KEY (interaction_type_id) REFERENCES interaction_types(interaction_type_id),
                        INDEX idx_session_id (session_id),
                        INDEX idx_page_id (page_id),
                        INDEX idx_interaction_type (interaction_type_id),
                        INDEX idx_interaction_time (interaction_time)
                    ) ENGINE=InnoDB
                """)
                
                # 1NF: Support ticket statuses table (separate entity)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ticket_statuses (
                        status_id INT AUTO_INCREMENT PRIMARY KEY,
                        status_name VARCHAR(50) NOT NULL UNIQUE,
                        description TEXT,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB
                """)
                
                # 1NF: Support ticket priorities table (separate entity)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ticket_priorities (
                        priority_id INT AUTO_INCREMENT PRIMARY KEY,
                        priority_name VARCHAR(50) NOT NULL UNIQUE,
                        priority_level INT NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB
                """)
                
                # 2NF: Support tickets table (no partial dependencies)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS support_tickets (
                        ticket_id VARCHAR(50) PRIMARY KEY,
                        user_id VARCHAR(50) NOT NULL,
                        assigned_admin_id VARCHAR(50),
                        status_id INT NOT NULL DEFAULT 1,
                        priority_id INT NOT NULL DEFAULT 2,
                        subject VARCHAR(500) NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        closed_at TIMESTAMP NULL,
                        is_active BOOLEAN DEFAULT TRUE,
                        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                        FOREIGN KEY (assigned_admin_id) REFERENCES users(user_id) ON DELETE SET NULL,
                        FOREIGN KEY (status_id) REFERENCES ticket_statuses(status_id),
                        FOREIGN KEY (priority_id) REFERENCES ticket_priorities(priority_id),
                        INDEX idx_user_id (user_id),
                        INDEX idx_assigned_admin (assigned_admin_id),
                        INDEX idx_status (status_id),
                        INDEX idx_priority (priority_id),
                        INDEX idx_created_at (created_at),
                        INDEX idx_is_active (is_active)
                    ) ENGINE=InnoDB
                """)
                
                # 1NF: Message types table (separate entity)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS message_types (
                        message_type_id INT AUTO_INCREMENT PRIMARY KEY,
                        type_name VARCHAR(50) NOT NULL UNIQUE,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB
                """)
                
                # 3NF: Chat messages table (no transitive dependencies)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS chat_messages (
                        message_id INT AUTO_INCREMENT PRIMARY KEY,
                        ticket_id VARCHAR(50) NOT NULL,
                        sender_id VARCHAR(50) NOT NULL,
                        message_type_id INT NOT NULL DEFAULT 1,
                        message_content TEXT NOT NULL,
                        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_read BOOLEAN DEFAULT FALSE,
                        is_deleted BOOLEAN DEFAULT FALSE,
                        edited_at TIMESTAMP NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (ticket_id) REFERENCES support_tickets(ticket_id) ON DELETE CASCADE,
                        FOREIGN KEY (sender_id) REFERENCES users(user_id) ON DELETE CASCADE,
                        FOREIGN KEY (message_type_id) REFERENCES message_types(message_type_id),
                        INDEX idx_ticket_id (ticket_id),
                        INDEX idx_sender_id (sender_id),
                        INDEX idx_sent_at (sent_at),
                        INDEX idx_is_read (is_read),
                        INDEX idx_is_deleted (is_deleted)
                    ) ENGINE=InnoDB
                """)
                
                # Insert default data
                self._insert_default_data(cursor)
                
                conn.commit()
                return True
                
        except Error as e:
            st.error(f"Database initialization error: {e}")
            return False
    
    def _insert_default_data(self, cursor):
        """Insert default reference data"""
        
        # Default ticket statuses
        statuses = [
            ('open', 'Ticket is open and awaiting response'),
            ('in_progress', 'Ticket is being worked on'),
            ('waiting_user', 'Waiting for user response'),
            ('resolved', 'Ticket has been resolved'),
            ('closed', 'Ticket is closed')
        ]
        
        for status_name, description in statuses:
            cursor.execute("""
                INSERT IGNORE INTO ticket_statuses (status_name, description)
                VALUES (%s, %s)
            """, (status_name, description))
        
        # Default ticket priorities
        priorities = [
            ('low', 1, 'Low priority issue'),
            ('medium', 2, 'Medium priority issue'),
            ('high', 3, 'High priority issue'),
            ('urgent', 4, 'Urgent issue requiring immediate attention')
        ]
        
        for priority_name, level, description in priorities:
            cursor.execute("""
                INSERT IGNORE INTO ticket_priorities (priority_name, priority_level, description)
                VALUES (%s, %s, %s)
            """, (priority_name, level, description))
        
        # Default interaction types
        interaction_types = [
            ('button_click', 'User clicked a button'),
            ('form_submit', 'User submitted a form'),
            ('search', 'User performed a search'),
            ('download', 'User downloaded a file'),
            ('chart_interaction', 'User interacted with a chart'),
            ('page_scroll', 'User scrolled on a page'),
            ('feature_usage', 'User used a specific feature')
        ]
        
        for type_name, description in interaction_types:
            cursor.execute("""
                INSERT IGNORE INTO interaction_types (type_name, description)
                VALUES (%s, %s)
            """, (type_name, description))
        
        # Default message types
        message_types = [
            ('text', 'Regular text message'),
            ('system', 'System generated message'),
            ('file', 'File attachment message'),
            ('image', 'Image message')
        ]
        
        for type_name, description in message_types:
            cursor.execute("""
                INSERT IGNORE INTO message_types (type_name, description)
                VALUES (%s, %s)
            """, (type_name, description))
        
        # Default pages
        pages = [
            ('General Search', 'search', 'AI-powered and basic search functionality'),
            ('Stock Analysis', 'analytics', 'Stock market analysis and recommendations'),
            ('News Crawling', 'news', 'News crawling and aggregation'),
            ('Review Analysis', 'reviews', 'App review analysis and sentiment'),
            ('Admin Dashboard', 'admin', 'Administrative dashboard and controls'),
            ('Support Chat', 'support', 'User support and chat system')
        ]
        
        for page_name, category, description in pages:
            cursor.execute("""
                INSERT IGNORE INTO pages (page_name, page_category, description)
                VALUES (%s, %s, %s)
            """, (page_name, category, description))

class MySQLUserTracker:
    """MySQL-based user tracking system"""

    def __init__(self, db: MySQLDatabase):
        self.db = db

    def create_user(self, user_id: str, username: str, email: str = None,
                   first_name: str = None, last_name: str = None, role: str = 'user') -> bool:
        """Create or update user in database"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"USE {self.db.config['database']}")

                cursor.execute("""
                    INSERT INTO users (user_id, username, email, first_name, last_name, role)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    email = VALUES(email),
                    first_name = VALUES(first_name),
                    last_name = VALUES(last_name),
                    role = VALUES(role),
                    updated_at = CURRENT_TIMESTAMP
                """, (user_id, username, email, first_name, last_name, role))

                conn.commit()
                return True
        except Error as e:
            st.error(f"Error creating user: {e}")
            return False

    def start_session(self, user_id: str, browser_info: Dict) -> str:
        """Start a new user session"""
        session_id = str(uuid.uuid4())

        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"USE {self.db.config['database']}")

                # Get or create browser type
                browser_id = self._get_or_create_browser(cursor, browser_info.get('browser', 'Unknown'))

                # Get or create device type
                device_id = self._get_or_create_device(cursor, browser_info.get('device', 'Unknown'))

                # Create session
                cursor.execute("""
                    INSERT INTO user_sessions
                    (session_id, user_id, browser_id, device_id, ip_address, user_agent)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    session_id, user_id, browser_id, device_id,
                    browser_info.get('ip_address', ''), browser_info.get('user_agent', '')
                ))

                conn.commit()
                return session_id

        except Error as e:
            st.error(f"Error starting session: {e}")
            return None

    def _get_or_create_browser(self, cursor, browser_name: str) -> int:
        """Get or create browser type"""
        cursor.execute("SELECT browser_id FROM browser_types WHERE browser_name = %s", (browser_name,))
        result = cursor.fetchone()

        if result:
            return result[0]
        else:
            cursor.execute("INSERT INTO browser_types (browser_name) VALUES (%s)", (browser_name,))
            return cursor.lastrowid

    def _get_or_create_device(self, cursor, device_name: str) -> int:
        """Get or create device type"""
        cursor.execute("SELECT device_id FROM device_types WHERE device_name = %s", (device_name,))
        result = cursor.fetchone()

        if result:
            return result[0]
        else:
            cursor.execute("INSERT INTO device_types (device_name) VALUES (%s)", (device_name,))
            return cursor.lastrowid

class MySQLSupportSystem:
    """MySQL-based support system with persistent chat channels"""

    def __init__(self, db: MySQLDatabase):
        self.db = db

    def create_ticket(self, user_id: str, subject: str, description: str, priority: str = 'medium') -> str:
        """Create a new support ticket"""
        ticket_id = f"TICKET-{int(datetime.now().timestamp())}-{str(uuid.uuid4())[:8]}"

        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"USE {self.db.config['database']}")

                # Get priority ID
                cursor.execute("SELECT priority_id FROM ticket_priorities WHERE priority_name = %s", (priority,))
                priority_result = cursor.fetchone()
                priority_id = priority_result[0] if priority_result else 2  # Default to medium

                # Create ticket
                cursor.execute("""
                    INSERT INTO support_tickets
                    (ticket_id, user_id, subject, description, priority_id)
                    VALUES (%s, %s, %s, %s, %s)
                """, (ticket_id, user_id, subject, description, priority_id))

                # Add initial system message
                cursor.execute("""
                    INSERT INTO chat_messages
                    (ticket_id, sender_id, message_type_id, message_content)
                    VALUES (%s, %s, 2, %s)
                """, (ticket_id, user_id, f"Support ticket created: {subject}"))

                conn.commit()
                return ticket_id

        except Error as e:
            st.error(f"Error creating ticket: {e}")
            return None

    def add_chat_message(self, ticket_id: str, sender_id: str, message: str, message_type: str = 'text') -> bool:
        """Add a message to chat"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"USE {self.db.config['database']}")

                # Get message type ID
                cursor.execute("SELECT message_type_id FROM message_types WHERE type_name = %s", (message_type,))
                type_result = cursor.fetchone()
                message_type_id = type_result[0] if type_result else 1  # Default to text

                # Add message
                cursor.execute("""
                    INSERT INTO chat_messages
                    (ticket_id, sender_id, message_type_id, message_content)
                    VALUES (%s, %s, %s, %s)
                """, (ticket_id, sender_id, message_type_id, message))

                # Update ticket timestamp
                cursor.execute("""
                    UPDATE support_tickets
                    SET updated_at = CURRENT_TIMESTAMP
                    WHERE ticket_id = %s
                """, (ticket_id,))

                conn.commit()
                return True

        except Error as e:
            st.error(f"Error adding chat message: {e}")
            return False

    def get_user_active_tickets(self, user_id: str) -> pd.DataFrame:
        """Get user's active (non-closed) tickets"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"USE {self.db.config['database']}")

                query = """
                    SELECT
                        st.ticket_id,
                        st.subject,
                        st.description,
                        st.created_at,
                        st.updated_at,
                        ts.status_name,
                        tp.priority_name,
                        COUNT(cm.message_id) as message_count,
                        MAX(cm.sent_at) as last_message_time
                    FROM support_tickets st
                    JOIN ticket_statuses ts ON st.status_id = ts.status_id
                    JOIN ticket_priorities tp ON st.priority_id = tp.priority_id
                    LEFT JOIN chat_messages cm ON st.ticket_id = cm.ticket_id
                    WHERE st.user_id = %s AND st.is_active = TRUE
                    AND ts.status_name != 'closed'
                    GROUP BY st.ticket_id
                    ORDER BY st.updated_at DESC
                """

                return pd.read_sql(query, conn, params=(user_id,))

        except Error as e:
            st.error(f"Error getting user tickets: {e}")
            return pd.DataFrame()

    def get_ticket_chat(self, ticket_id: str) -> pd.DataFrame:
        """Get chat messages for a ticket"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"USE {self.db.config['database']}")

                query = """
                    SELECT
                        cm.message_id,
                        cm.message_content,
                        cm.sent_at,
                        u.username,
                        u.role,
                        mt.type_name as message_type,
                        cm.is_read
                    FROM chat_messages cm
                    JOIN users u ON cm.sender_id = u.user_id
                    JOIN message_types mt ON cm.message_type_id = mt.message_type_id
                    WHERE cm.ticket_id = %s AND cm.is_deleted = FALSE
                    ORDER BY cm.sent_at ASC
                """

                return pd.read_sql(query, conn, params=(ticket_id,))

        except Error as e:
            st.error(f"Error getting ticket chat: {e}")
            return pd.DataFrame()

    def close_ticket(self, ticket_id: str, closer_id: str) -> bool:
        """Close a support ticket"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"USE {self.db.config['database']}")

                # Update ticket status to closed
                cursor.execute("""
                    UPDATE support_tickets
                    SET status_id = (SELECT status_id FROM ticket_statuses WHERE status_name = 'closed'),
                        closed_at = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE ticket_id = %s
                """, (ticket_id,))

                # Add system message
                cursor.execute("""
                    INSERT INTO chat_messages
                    (ticket_id, sender_id, message_type_id, message_content)
                    VALUES (%s, %s, 2, 'Ticket has been closed')
                """, (ticket_id, closer_id))

                conn.commit()
                return True

        except Error as e:
            st.error(f"Error closing ticket: {e}")
            return False

# Global database instances
mysql_db = MySQLDatabase()
mysql_user_tracker = MySQLUserTracker(mysql_db)
mysql_support_system = MySQLSupportSystem(mysql_db)
