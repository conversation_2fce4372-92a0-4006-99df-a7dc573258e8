"""
Enhanced User Support Widget with Persistent Chat Channels
==========================================================

Provides persistent chat channels using MySQL database with proper 3NF design.
Chat channels persist until closed by admin or user.
"""

import streamlit as st
import time
import pandas as pd
from datetime import datetime
from mysql_database import mysql_db, mysql_user_tracker, mysql_support_system
from typing import Optional

def initialize_mysql_support():
    """Initialize MySQL database for support system"""
    if 'mysql_initialized' not in st.session_state:
        try:
            # Initialize database
            if mysql_db.initialize_database():
                st.session_state.mysql_initialized = True
                
                # Create user if logged in
                if st.session_state.get('authentication_status') and st.session_state.get('username'):
                    user_info = st.session_state.get('name', {})
                    mysql_user_tracker.create_user(
                        user_id=st.session_state['username'],
                        username=st.session_state['username'],
                        email=user_info.get('email', ''),
                        first_name=user_info.get('first_name', ''),
                        last_name=user_info.get('last_name', ''),
                        role='admin' if st.session_state.get('username') == 'admin' else 'user'
                    )
            else:
                st.error("Failed to initialize MySQL database")
        except Exception as e:
            st.error(f"MySQL initialization error: {e}")

def create_persistent_support_widget():
    """Create floating support widget with persistent chat channels"""
    
    # Initialize MySQL support
    initialize_mysql_support()
    
    # Initialize widget state
    if 'support_widget_open' not in st.session_state:
        st.session_state.support_widget_open = False
    if 'selected_ticket_id' not in st.session_state:
        st.session_state.selected_ticket_id = None
    
    # CSS for enhanced floating widget
    st.markdown("""
    <style>
    .support-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        font-family: 'Source Sans Pro', sans-serif;
        border: 1px solid #e0e0e0;
    }
    
    .support-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 50px;
        padding: 15px 25px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .support-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    }
    
    .support-window {
        width: 380px;
        height: 600px;
        background: white;
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
    
    .support-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .support-close {
        background: rgba(255,255,255,0.2);
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 5px 10px;
        border-radius: 50%;
        transition: background 0.2s ease;
    }
    
    .support-close:hover {
        background: rgba(255,255,255,0.3);
    }
    
    .ticket-list {
        max-height: 200px;
        overflow-y: auto;
        border-bottom: 1px solid #eee;
    }
    
    .ticket-item {
        padding: 12px 15px;
        border-bottom: 1px solid #f5f5f5;
        cursor: pointer;
        transition: background 0.2s ease;
    }
    
    .ticket-item:hover {
        background: #f8f9fa;
    }
    
    .ticket-item.active {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    
    .ticket-subject {
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
    }
    
    .ticket-meta {
        font-size: 12px;
        color: #666;
    }
    
    .chat-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
    
    .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        max-height: 300px;
    }
    
    .message {
        margin-bottom: 12px;
        padding: 10px 12px;
        border-radius: 12px;
        max-width: 85%;
        word-wrap: break-word;
    }
    
    .message.user {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        margin-left: auto;
        text-align: right;
    }
    
    .message.admin {
        background: #f5f5f5;
        color: #333;
        margin-right: auto;
    }
    
    .message.system {
        background: #fff3cd;
        color: #856404;
        text-align: center;
        font-style: italic;
        margin: 0 auto;
    }
    
    .message-time {
        font-size: 10px;
        opacity: 0.7;
        margin-top: 4px;
    }
    
    .chat-input-area {
        padding: 15px;
        border-top: 1px solid #eee;
        background: #fafafa;
    }
    
    .new-ticket-form {
        padding: 20px;
        background: #fafafa;
    }
    
    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 15px;
    }
    
    .quick-action {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        color: #1976d2;
        border-radius: 20px;
        padding: 6px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .quick-action:hover {
        background: #2196f3;
        color: white;
    }
    
    .unread-badge {
        background: #f44336;
        color: white;
        border-radius: 50%;
        padding: 2px 6px;
        font-size: 10px;
        font-weight: bold;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Support widget container
    if not st.session_state.support_widget_open:
        # Floating support button
        if st.button("💬 Need Help?", key="support_toggle", help="Click for support"):
            st.session_state.support_widget_open = True
            st.rerun()
    else:
        # Support window
        create_support_window()

def create_support_window():
    """Create the main support window with persistent chat channels"""
    
    # Check authentication
    if not st.session_state.get('authentication_status'):
        st.warning("Please log in to use support chat")
        if st.button("Close", key="support_close_auth"):
            st.session_state.support_widget_open = False
            st.rerun()
        return
    
    user_id = st.session_state.get('username')
    
    # Header
    col1, col2 = st.columns([4, 1])
    with col1:
        st.markdown("### 💬 Support Center")
    with col2:
        if st.button("✕", key="support_close", help="Close support"):
            st.session_state.support_widget_open = False
            st.session_state.selected_ticket_id = None
            st.rerun()
    
    # Get user's active tickets
    active_tickets = mysql_support_system.get_user_active_tickets(user_id)
    
    if not active_tickets.empty:
        # Show existing tickets
        st.markdown("**Your Active Conversations:**")
        
        # Ticket list
        for _, ticket in active_tickets.iterrows():
            ticket_id = ticket['ticket_id']
            subject = ticket['subject']
            last_message = ticket['last_message_time']
            message_count = ticket['message_count']
            status = ticket['status_name']
            
            # Ticket item
            is_selected = st.session_state.selected_ticket_id == ticket_id
            
            with st.container():
                col1, col2, col3 = st.columns([3, 1, 1])
                
                with col1:
                    if st.button(
                        f"📋 {subject[:30]}..." if len(subject) > 30 else f"📋 {subject}",
                        key=f"ticket_{ticket_id}",
                        type="primary" if is_selected else "secondary"
                    ):
                        st.session_state.selected_ticket_id = ticket_id
                        st.rerun()
                
                with col2:
                    st.caption(f"{message_count} msgs")
                
                with col3:
                    if st.button("🗑️", key=f"close_{ticket_id}", help="Close ticket"):
                        mysql_support_system.close_ticket(ticket_id, user_id)
                        if st.session_state.selected_ticket_id == ticket_id:
                            st.session_state.selected_ticket_id = None
                        st.success("Ticket closed!")
                        time.sleep(1)
                        st.rerun()
        
        st.divider()
    
    # Show selected ticket chat or new ticket form
    if st.session_state.selected_ticket_id:
        show_ticket_chat(st.session_state.selected_ticket_id, user_id)
    else:
        show_new_ticket_form(user_id)

def show_ticket_chat(ticket_id: str, user_id: str):
    """Show chat interface for selected ticket"""
    
    st.markdown(f"**Chat: {ticket_id}**")
    
    # Get chat messages
    messages = mysql_support_system.get_ticket_chat(ticket_id)
    
    # Messages container
    if not messages.empty:
        st.markdown("**Messages:**")
        
        for _, msg in messages.iterrows():
            sender_role = msg['role']
            username = msg['username']
            content = msg['message_content']
            sent_at = msg['sent_at']
            msg_type = msg['message_type']
            
            # Message display
            if sender_role == 'user':
                if msg_type == 'system':
                    st.info(f"🔔 {content}")
                else:
                    st.markdown(f"""
                    <div style="text-align: right; margin-bottom: 10px;">
                        <div style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                                   color: white; padding: 8px 12px; border-radius: 12px; max-width: 80%;">
                            <strong>You</strong><br>{content}
                            <div style="font-size: 10px; opacity: 0.8; margin-top: 4px;">{sent_at}</div>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
            else:  # admin
                st.markdown(f"""
                <div style="text-align: left; margin-bottom: 10px;">
                    <div style="display: inline-block; background: #f5f5f5; color: #333; 
                               padding: 8px 12px; border-radius: 12px; max-width: 80%;">
                        <strong>🔧 Support ({username})</strong><br>{content}
                        <div style="font-size: 10px; opacity: 0.6; margin-top: 4px;">{sent_at}</div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
    
    # Message input
    st.markdown("---")
    new_message = st.text_area(
        "Type your message:",
        placeholder="Type your reply here...",
        height=80,
        key=f"message_input_{ticket_id}"
    )
    
    # Send button
    col1, col2 = st.columns([2, 1])
    
    with col1:
        if st.button("📤 Send Message", key=f"send_{ticket_id}"):
            if new_message.strip():
                if mysql_support_system.add_chat_message(ticket_id, user_id, new_message):
                    st.success("Message sent!")
                    time.sleep(1)
                    st.rerun()
                else:
                    st.error("Failed to send message")
            else:
                st.error("Please enter a message")
    
    with col2:
        if st.button("🔄 Refresh", key=f"refresh_{ticket_id}"):
            st.rerun()

def show_new_ticket_form(user_id: str):
    """Show form to create new support ticket"""
    
    st.markdown("**Start New Conversation:**")
    
    # Quick action buttons
    st.markdown("**Quick Actions:**")
    quick_actions = st.columns(3)
    
    with quick_actions[0]:
        if st.button("🐛 Report Bug", key="quick_bug"):
            st.session_state.quick_subject = "Bug Report"
            st.session_state.quick_message = "I found a bug in the application. Here are the details:"
    
    with quick_actions[1]:
        if st.button("❓ Ask Question", key="quick_question"):
            st.session_state.quick_subject = "Question"
            st.session_state.quick_message = "I have a question about how to use:"
    
    with quick_actions[2]:
        if st.button("💡 Feature Request", key="quick_feature"):
            st.session_state.quick_subject = "Feature Request"
            st.session_state.quick_message = "I would like to suggest a new feature:"
    
    # Form fields
    subject = st.text_input(
        "Subject:",
        value=st.session_state.get('quick_subject', ''),
        placeholder="Brief description of your issue",
        key="new_ticket_subject"
    )
    
    message = st.text_area(
        "Message:",
        value=st.session_state.get('quick_message', ''),
        placeholder="Describe your issue or question in detail...",
        height=100,
        key="new_ticket_message"
    )
    
    priority = st.selectbox(
        "Priority:",
        ["low", "medium", "high", "urgent"],
        index=1,
        key="new_ticket_priority"
    )
    
    # Create ticket button
    if st.button("🚀 Start Conversation", key="create_new_ticket"):
        if subject.strip() and message.strip():
            ticket_id = mysql_support_system.create_ticket(
                user_id=user_id,
                subject=subject,
                description=message,
                priority=priority
            )
            
            if ticket_id:
                # Add initial user message
                mysql_support_system.add_chat_message(ticket_id, user_id, message)
                
                st.session_state.selected_ticket_id = ticket_id
                
                # Clear quick action state
                if 'quick_subject' in st.session_state:
                    del st.session_state.quick_subject
                if 'quick_message' in st.session_state:
                    del st.session_state.quick_message
                
                st.success("✅ Conversation started! An admin will respond soon.")
                time.sleep(1)
                st.rerun()
            else:
                st.error("Failed to create support ticket")
        else:
            st.error("Please fill in both subject and message")
