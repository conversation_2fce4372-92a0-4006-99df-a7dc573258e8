import asyncio
import streamlit as st
import pandas as pd
import json
from ui_ux import UI
from general_search import crawl_google_results
from stock_analysis import get_all_vietstock_data, merge_stock_with_all_news, analyze_stock
from crawl_news import run_extraction
from review_analysis import fetch_google_play_reviews, fetch_app_store_reviews, load_sentiment_model, process_sentiment_dataframe, sentiment_to_position_3, predict_sentiment_multilingual, sentiment_to_position_5, plot_sentiment_scale
import numpy as  np
import yaml
import streamlit_authenticator as stauth

# Import admin system and user support
from admin_system import AdminAuth, create_admin_dashboard
from user_support_widget import create_support_chat_widget, initialize_user_session, track_page_visit, track_user_interaction

# def main():

#     UI()
    
#     # Navigation
#     st.sidebar.title("Navigation bar")
#     from streamlit_option_menu import option_menu
#     with st.sidebar:
#         page = option_menu(
#             menu_title=None, 
#             options=['Search', 'Reviews', 'News', 'Jobs', 'Stock'], 
#             icons=['search','megaphone','newspaper','person-workspace','cash'], 
#             menu_icon='three-dots', 
#             default_index=0, 
#             orientation='vertical',
#             styles={
#                 'container':{'padding-top':'30px','background-color':'#A979BF'},
#                 'icon':{'color':'black','font-size':'18px'},
#                 'nav-link': {
#                     'font-size':'18px',
#                     'text-align':'left',
#                     'margin': '0px',
#                     'line-height': '0.5',
#                     '--hover-color': '#eee',
#                     },
#                 'nav-link-selected': {'background-color':'#dabb42'},
#                 },
#             )

#     if page == "Search":
        
#         st.title("Keyword Search Tool")

#         # Initialize session state for results
#         if "results" not in st.session_state:
#             st.session_state.results = None

#         keyword = st.sidebar.text_input("Enter search keyword:")
#         pages = st.sidebar.number_input("Number of pages to crawl:", min_value=1, max_value=100, value=None)

#         if st.sidebar.button("Search"):
#             if keyword:
#                 with st.spinner("Crawling search results..."):
#                     results = asyncio.run(crawl_google_results(keyword, pages=pages))
#                     if results:
#                         st.session_state.results = results  # Store results in session state
#                     else:
#                         st.warning("No results found.")
#             else:
#                 st.warning("Please enter a search keyword.")

#         # Display stored results
#         if st.session_state.results:
#             df = pd.DataFrame(st.session_state.results)
#             st.write(df)

#             # Prepare CSV download
#             csv = df.to_csv(index=False)
#             st.download_button(
#                 label="Download results as CSV",
#                 data=csv,
#                 file_name=f"search_results_{keyword}.csv",
#                 mime="text/csv",
#             )
    
#     elif page == "Reviews":
#         if "original_df" not in st.session_state:
#             st.session_state.original_df = None
#         if "analyzed_df" not in st.session_state:
#             st.session_state.analyzed_df = None

#         # Sidebar: Scraper selection (not modified here)
#         scraper_selection = st.sidebar.radio("Select a scraper:", ["Google Play", "App Store"], horizontal=True)
#         num_reviews = st.sidebar.number_input("Enter the number of reviews to fetch:", min_value=1, value=100)

#         if scraper_selection == "Google Play":
#             app_url = st.sidebar.text_input("Enter the Google Play app URL:")
#             if st.sidebar.button("Fetch Google Play Reviews"):
#                 if app_url:
#                     st.session_state.original_df = fetch_google_play_reviews(app_url, num_reviews)
#                     st.session_state.analyzed_df = None
#                 else:
#                     st.error("Please enter a valid Google Play app URL.")
#         elif scraper_selection == "App Store":
#             app_id = st.sidebar.text_input("Enter the App Store app ID:")
#             if st.sidebar.button("Fetch App Store Reviews"):
#                 if app_id:
#                     st.session_state.original_df = fetch_app_store_reviews(app_id, num_reviews)
#                     st.session_state.analyzed_df = None
#                 else:
#                     st.error("Please enter a valid App Store app ID.")

#         if st.session_state.original_df is not None:
#             st.subheader("Original Reviews")
#             st.dataframe(st.session_state.original_df)

#             # Analysis: Only show Analyze button if analysis hasn't been done.
#             # Initialize persistent session state variables if not present
#             if "analyzed_df" not in st.session_state:
#                 st.session_state.analyzed_df = None
#             if "model_option" not in st.session_state:
#                 st.session_state.model_option = "Vietnamese"  # default value

#             # Define reset function that clears analysis state
#             def reset_analysis():
#                 st.session_state.analyzed_df = None
#                 st.session_state.overall_score = None
#                 st.session_state.overall_label = None

#             # Place the model selection radio unconditionally (for example, inside an expander)
#             with st.expander("Sentiment Analysis Settings", expanded=True):
#                 st.radio(
#                     "Select Sentiment Model:",
#                     ["Vietnamese", "Multilingual"],
#                     key="model_option",
#                     on_change=reset_analysis  # Reset analysis when model changes.
#                 )

#             # Show the Analyze button only if analysis hasn't been done.
#             if st.session_state.analyzed_df is None:
#                 if st.button("Analyze Sentiments"):
#                     df = st.session_state.original_df.copy()
#                     if st.session_state.model_option == "Vietnamese":
#                         model_path = "5CD-AI/Vietnamese-Sentiment-visobert"
#                         tokenizer, config, model = load_sentiment_model(model_path)
#                         df = process_sentiment_dataframe(df, tokenizer, config, model)
#                         positions = df["Dominant Sentiment"].apply(sentiment_to_position_3)
#                         segments = [(0,33,"Negative"), (33,67,"Neutral"), (67,100,"Positive")]
#                         overall_score = np.mean(positions)
#                         if overall_score < 33:
#                             overall_label = "Negative"
#                         elif overall_score < 67:
#                             overall_label = "Neutral"
#                         else:
#                             overall_label = "Positive"
#                     else:  # Multilingual (5 classes)
#                         model_path = "tabularisai/multilingual-sentiment-analysis"
#                         tokenizer, config, model = load_sentiment_model(model_path)
#                         results = df["review"].apply(lambda x: predict_sentiment_multilingual(x, tokenizer, model))
#                         df[["Dominant Sentiment", "Confidence"]] = results.values.tolist()
#                         positions = df["Dominant Sentiment"].apply(sentiment_to_position_5)
#                         overall_score = np.average(positions, weights=df["Confidence"] / 100)
#                         if overall_score < 20:
#                             overall_label = "Very Negative"
#                         elif overall_score < 40:
#                             overall_label = "Negative"
#                         elif overall_score < 60:
#                             overall_label = "Neutral"
#                         elif overall_score < 80:
#                             overall_label = "Positive"
#                         else:
#                             overall_label = "Very Positive"
#                         segments = None  # Use default 5-segment scheme
#                     st.session_state.analyzed_df = df
#                     st.session_state.overall_score = overall_score
#                     st.session_state.overall_label = overall_label

#             # Later, display analyzed data and the overall chart if available.
#             if st.session_state.analyzed_df is not None:
#                 st.subheader("Analyzed Reviews")
#                 st.dataframe(st.session_state.analyzed_df)
#                 st.subheader("Overall Sentiment Score")
#                 st.markdown(f"**Overall Score: {st.session_state.overall_score:.2f}** ({st.session_state.overall_label})")
#                 # For Vietnamese model, use custom segments; otherwise, None.
#                 segments_to_use = segments if st.session_state.model_option == "Vietnamese" else None
#                 fig = plot_sentiment_scale(
#                     st.session_state.overall_score, st.session_state.overall_label,
#                     sentiment_score=st.session_state.overall_score,
#                     segments=segments_to_use
#                 )
#                 st.plotly_chart(fig, use_container_width=True, key="overall_chart")


#     elif page == "News":

#         st.title("News Crawler")

#         if "data" not in st.session_state:
#             st.session_state.data = None

#         if st.sidebar.button("Get Data"):
#             with st.spinner("Crawling articles..."):
#                 data = run_extraction()
#                 if data:
#                     st.session_state.data = data
#                     st.success("Data extraction complete!")
#                 else:
#                     st.warning("No data found.")

#         # Display results
#         if st.session_state.data:
#             df = pd.DataFrame(st.session_state.data)
#             st.write(df)

#             csv = df.to_csv(index=False)
#             st.download_button(
#                 label="Download Data as CSV",
#                 data=csv,
#                 file_name="news_articles.csv",
#                 mime="text/csv",
#             )

#     elif page == "Stock":
#         from vnstock import Vnstock
#         stock = Vnstock().stock(symbol='VND', source='VCI')
#         import datetime

#         st.sidebar.title("Stock Data")
#         today = datetime.datetime.now()
#         stock_symbol = st.sidebar.selectbox("Stock Symbol", stock.listing.all_symbols()['ticker'])
#         start_date = st.sidebar.date_input("Start Date", datetime.date(2024, 1, 1),format='YYYY-MM-DD')
#         start_date = start_date.strftime('%Y-%m-%d')
#         end_date = st.sidebar.date_input("End Date", today, format='YYYY-MM-DD')
#         end_date = end_date.strftime('%Y-%m-%d')

#         if st.sidebar.button('Get Data'):
#             stock_df = stock.quote.history(symbol=stock_symbol, start=start_date, end=end_date)
#             stock_df['time'] = stock_df['time'].apply(lambda x: x.strftime('%d/%m/%Y'))
#             # Get news data from all Vietstock subdomains
#             news_df = get_all_vietstock_data(
#                 symbol=stock_symbol,
#                 start_date=start_date,
#                 end_date=end_date
#             )
#             # Merge the data
#             merged_df = merge_stock_with_all_news(stock_df, news_df)
#             st.markdown("<h4 style='text-align: center; font-size: 20px; background-image: linear-gradient(to right, #96d9a4, #c23640); color:#061c04;'>"
#                         "Stock data with news</h4>", unsafe_allow_html=True)  
#             st.dataframe(merged_df, hide_index=True, width=2000)

#             import plotly.graph_objects as go
#             fig = go.Figure(data=[go.Candlestick(
#                 x=merged_df['time'],
#                 open=merged_df['open'],
#                 high=merged_df['high'],
#                 low=merged_df['low'],
#                 close=merged_df['close']
#             )])

#             # Customize the x-axis to show only months
#             fig.update_layout(
#                 xaxis=dict(
#                     tickformat='%b %Y',  # Format: Month Year (e.g., Jan 2025)
#                     tickmode='auto',
#                     nticks=12,  # Adjust this number to control how many tick marks to show
#                 )
#             )

#             st.plotly_chart(fig, use_container_width=True)

#             # # Calculate daily price fluctuation
#             # merged_df["price_fluctuation"] = merged_df["close"].diff()
#             # # Forward fill news and stakeholder events to associate each day with the latest event
#             # merged_df["latest_news_newest_news"] = merged_df["news_newest_news"].replace("", None).ffill()
#             # merged_df["latest_news_business_results"] = merged_df["news_business_results"].replace("", None).ffill()
#             # merged_df["latest_news_dividend"] = merged_df["news_dividend"].replace("", None).ffill()
#             # merged_df["latest_news_internal_stakeholders"] = merged_df["news_internal_stakeholders"].replace("", None).ffill()
#             # merged_df["latest_news_personnel"] = merged_df["news_personnel"].replace("", None).ffill()
#             # merged_df["price_fluctuation_pct"] = merged_df["price_fluctuation"] / merged_df["close"].shift(1) * 100
#             # st.dataframe(merged_df, hide_index=True)

#             from datetime import datetime
#             # Function to transform DataFrame
#             def transform_dataframe(df):
#                 events = []
                
#                 for _, row in df.iterrows():
#                     if not row["all_news"]:  # Skip empty events
#                         continue  

#                     # Convert date format
#                     date_obj = datetime.strptime(row["time"], "%d/%m/%Y")

#                     # Ensure all_news is a string (join list items if necessary)
#                     if isinstance(row["all_news"], list):
#                         formatted_news = "<br>".join(row["all_news"])  # Join list elements with line breaks
#                     else:
#                         formatted_news = str(row["all_news"]).replace(",", "<br>")  # Handle string case

#                     event = {
#                         "text": {
#                             "headline": "Events of the day",  # Customize if needed
#                             "text": formatted_news  # Use formatted text
#                         },
#                         "start_date": {
#                             "year": str(date_obj.year),
#                             "month": str(date_obj.month),
#                             "day": str(date_obj.day)
#                         }
#                     }
#                     events.append(event)

#                 return {"events": events}

#             # Transform and save the result
#             transformed_data = transform_dataframe(merged_df)

#             # Save to JSON file
#             with open("output.json", "w", encoding="utf-8") as f:
#                 json.dump(transformed_data, f, ensure_ascii=False, indent=2)

#             import streamlit.components.v1 as components
#             # parameters
#             CDN_PATH = 'https://cdn.knightlab.com/libs/timeline3/latest'
#             JSON_PATH = 'output.json'

#             TL_HEIGHT = 450  # px

#             # load data
#             with open(JSON_PATH, "r") as f:
#                 json_text = f.read()
#                 source_param = 'timeline_json'
#                 source_block = f'var {source_param} = {json_text};'

#             # load css + js
#             css_block = f'<link title="timeline-styles" rel="stylesheet" href="{CDN_PATH}/css/timeline.css">'
#             js_block = f'<script src="{CDN_PATH}/js/timeline.js"></script>'

#             # write html block
#             htmlcode = css_block + ''' 
#             ''' + js_block + '''
#                 <div id='timeline-embed' style="width: 95%; height: ''' + str(TL_HEIGHT) + '''px; margin: 1px;"></div>
#                 <script type="text/javascript">
#                     var additionalOptions = {
#                         start_at_end: false, is_embed: true,
#                     }
#                     ''' + source_block + '''
#                     timeline = new TL.Timeline('timeline-embed', ''' + source_param + ''', additionalOptions);
#                 </script>'''

#             components.html(htmlcode, height=TL_HEIGHT)

#             # EXPANDERS
#             df = merged_df.copy()
#             analyze_stock(df, symbol=stock_symbol)
            
#             with st.expander('Other metrics'):
#                 try:
#                     # Get stock data
#                     stock_df_copied = stock_df[['time', 'close']].rename(columns={'close': stock_symbol})
#                     stock_df_copied['time'] = pd.to_datetime(stock_df_copied['time'], errors='coerce').dt.strftime('%Y-%m-%d')
                    
#                     # Get VNINDEX data
#                     try:
#                         vnindex = stock.quote.history(symbol='VNINDEX', start=start_date, end=end_date)
#                         # Check if VNINDEX data was retrieved successfully
#                         if vnindex.empty:
#                             st.warning("Could not retrieve VNINDEX data for the selected time period")
#                             raise ValueError("Empty VNINDEX data")
                            
#                         vnindex = vnindex[['time', 'close']].rename(columns={'close': 'VNINDEX'})
#                         vnindex['time'] = pd.to_datetime(vnindex['time'], errors='coerce').dt.strftime('%Y-%m-%d')
#                     except Exception as e:
#                         st.error(f"Error fetching VNINDEX data: {str(e)}")
#                         st.info("Beta calculation requires VNINDEX data for comparison")
#                         raise
                    
#                     # Merge data
#                     final_df = pd.merge(stock_df_copied, vnindex, on='time', how='inner').set_index('time')
                    
#                     # Check if merge resulted in data
#                     if final_df.empty:
#                         st.warning("No overlapping dates between stock data and VNINDEX")
#                         raise ValueError("No overlapping data")
                        
#                     # Reverse order
#                     final_df = final_df.iloc[::-1]
                    
#                     # Display original dataset
#                     col1, col2 = st.columns(2)
#                     with col1:
#                         st.subheader("Original Dataset")
#                         st.dataframe(final_df)
                    
#                     # Define daily return function with proper error handling
#                     def daily_return(df):
#                         if df.empty:
#                             return pd.DataFrame()
                            
#                         # Calculate percent change
#                         df_daily = df.pct_change() * 100
                        
#                         # Only set the first row to 0 if the DataFrame has rows
#                         if len(df_daily) > 0:
#                             df_daily.iloc[0] = 0
                            
#                         return df_daily
                    
#                     # Calculate daily returns
#                     data_daily_return = daily_return(final_df)
                    
#                     # Display daily returns
#                     with col2:
#                         st.subheader("Daily Return Dataset")
#                         st.dataframe(data_daily_return)
                    
#                     # Define beta calculation with error handling
#                     def calculate_beta(data_daily_return, stock_col):
#                         try:
#                             # Verify columns exist
#                             if 'VNINDEX' not in data_daily_return.columns:
#                                 st.warning("VNINDEX column not found in data")
#                                 return 0, 0
                                
#                             if stock_col not in data_daily_return.columns:
#                                 st.warning(f"{stock_col} column not found in data")
#                                 return 0, 0
                            
#                             # Drop NaN values for clean calculation
#                             valid_data = data_daily_return[['VNINDEX', stock_col]].dropna()
                            
#                             if len(valid_data) < 2:
#                                 st.warning("Not enough valid data points to calculate beta")
#                                 return 0, 0
                            
#                             # Calculate beta using linear regression
#                             beta, alpha = np.polyfit(valid_data['VNINDEX'], valid_data[stock_col], 1)
#                             return beta, alpha
#                         except Exception as e:
#                             st.error(f"Error calculating beta: {str(e)}")
#                             return 0, 0
                    
#                     # Calculate beta
#                     beta, alpha = calculate_beta(data_daily_return, stock_symbol)
                    
#                     # Display beta
#                     with col1:
#                         st.subheader("Beta Value")
#                         if beta != 0:
#                             st.write(f"Beta for {stock_symbol}: {round(beta, 2)}")
#                         else:
#                             st.write("Could not calculate beta")
                    
#                     # Only calculate CAPM if we have a valid beta
#                     if beta != 0:
#                         # Calculate CAPM expected return
#                         rf = 0  # Risk-free rate
#                         rm = data_daily_return['VNINDEX'].mean() * 252  # Market return (annualized)
#                         expected_return = rf + beta * (rm - rf)
                        
#                         # Display CAPM expected return
#                         with col2:
#                             st.subheader("CAPM Expected Return")
#                             st.write(f"Expected return for {stock_symbol}: {round(expected_return, 2)}%")
                            
#                 except Exception as e:
#                     st.error(f"Error in metrics calculation: {str(e)}")
#                     st.info("Please check your data and try again")
                



#     elif page == "Jobs":
#         pass

# if __name__ == "__main__":
#     main()
    

def use_general_search():
    """Enhanced general search with AI-powered analytics"""
    # Track page visit
    track_page_visit("General Search")

    from streamlit_option_menu import option_menu

    # Search mode selection
    search_mode = option_menu(
        None,
        ["🔍 Basic Search", "🚀 AI-Powered Search"],
        icons=['search', 'robot'],
        menu_icon="cast",
        default_index=1,
        orientation="horizontal",
        styles={
            "container": {"padding": "0!important", "background-color": "#ffffff"},
            "icon": {"color": "#fcc603", "font-size": "20px"},
            "nav-link": {"font-size": "16px", "text-align": "left", "margin":"0px", "--hover-color": "#eee"},
            "nav-link-selected": {"background-color": "#69bd68"},
        }
    )

    if search_mode == "🔍 Basic Search":
        # Original basic search functionality
        st.title("🔍 Basic Keyword Search Tool")

        # Initialize session state for results
        if "results" not in st.session_state:
            st.session_state.results = None

        keyword = st.sidebar.text_input("Enter search keyword:")
        pages = st.sidebar.number_input("Number of pages to crawl:", min_value=1, max_value=10, value=3)

        if st.sidebar.button("Search"):
            if keyword:
                with st.spinner("Crawling search results..."):
                    from general_search import crawl_google_results
                    results = asyncio.run(crawl_google_results(keyword, pages=pages))
                    if results:
                        st.session_state.results = results  # Store results in session state
                        st.success(f"Found {len(results)} results!")
                    else:
                        st.warning("No results found.")
            else:
                st.warning("Please enter a search keyword.")

        # Display stored results
        if st.session_state.results:
            df = pd.DataFrame(st.session_state.results)
            st.subheader("Search Results")
            st.dataframe(df, use_container_width=True, hide_index=True)

            # Prepare CSV download
            csv = df.to_csv(index=False)
            st.download_button(
                label="📄 Download results as CSV",
                data=csv,
                file_name=f"search_results_{keyword}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv",
                mime="text/csv",
            )

    else:
        # Enhanced AI-powered search
        from enhanced_search import create_advanced_search_interface
        create_advanced_search_interface()

def use_review_analysis():
    # Enhanced review analysis interface
    analysis_mode = st.radio(
        "Choose Analysis Mode:",
        ["🚀 Enhanced Analysis (Recommended)", "📊 Basic Analysis"],
        horizontal=True,
        help="Enhanced mode includes app search, advanced dashboards, and comprehensive analytics"
    )

    if analysis_mode == "🚀 Enhanced Analysis (Recommended)":
        from enhanced_review_analysis import create_enhanced_review_analysis_interface
        create_enhanced_review_analysis_interface()
    else:
        # Original basic analysis
        use_basic_review_analysis()

def use_basic_review_analysis():
    """Original basic review analysis functionality"""
    if "original_df" not in st.session_state:
        st.session_state.original_df = None
    if "analyzed_df" not in st.session_state:
        st.session_state.analyzed_df = None

    # Sidebar: Scraper selection (not modified here)
    scraper_selection = st.sidebar.radio("Select a scraper:", ["Google Play", "App Store"], horizontal=True)
    num_reviews = st.sidebar.number_input("Enter the number of reviews to fetch:", min_value=1, value=100)

    if scraper_selection == "Google Play":
        app_url = st.sidebar.text_input("Enter the Google Play app URL:")
        if st.sidebar.button("Fetch Google Play Reviews"):
            if app_url:
                st.session_state.original_df = fetch_google_play_reviews(app_url, num_reviews)
                st.session_state.analyzed_df = None
            else:
                st.error("Please enter a valid Google Play app URL.")
    elif scraper_selection == "App Store":
        app_id = st.sidebar.text_input("Enter the App Store app ID:")
        if st.sidebar.button("Fetch App Store Reviews"):
            if app_id:
                st.session_state.original_df = fetch_app_store_reviews(app_id, num_reviews)
                st.session_state.analyzed_df = None
            else:
                st.error("Please enter a valid App Store app ID.")

    if st.session_state.original_df is not None:
        st.subheader("Original Reviews")
        st.dataframe(st.session_state.original_df)

        # Analysis: Only show Analyze button if analysis hasn't been done.
        # Initialize persistent session state variables if not present
        if "analyzed_df" not in st.session_state:
            st.session_state.analyzed_df = None
        if "model_option" not in st.session_state:
            st.session_state.model_option = "Vietnamese"  # default value

        # Define reset function that clears analysis state
        def reset_analysis():
            st.session_state.analyzed_df = None
            st.session_state.overall_score = None
            st.session_state.overall_label = None

        # Place the model selection radio unconditionally (for example, inside an expander)
        with st.expander("Sentiment Analysis Settings", expanded=True):
            st.radio(
                "Select Sentiment Model:",
                ["Vietnamese", "Multilingual"],
                key="model_option",
                on_change=reset_analysis  # Reset analysis when model changes.
            )

        # Show the Analyze button only if analysis hasn't been done.
        if st.session_state.analyzed_df is None:
            if st.button("Analyze Sentiments"):
                df = st.session_state.original_df.copy()
                if st.session_state.model_option == "Vietnamese":
                    model_path = "5CD-AI/Vietnamese-Sentiment-visobert"
                    tokenizer, config, model = load_sentiment_model(model_path)
                    df = process_sentiment_dataframe(df, tokenizer, config, model)
                    positions = df["Dominant Sentiment"].apply(sentiment_to_position_3)
                    segments = [(0,33,"Negative"), (33,67,"Neutral"), (67,100,"Positive")]
                    overall_score = np.mean(positions)
                    if overall_score < 33:
                        overall_label = "Negative"
                    elif overall_score < 67:
                        overall_label = "Neutral"
                    else:
                        overall_label = "Positive"
                else:  # Multilingual (5 classes)
                    model_path = "tabularisai/multilingual-sentiment-analysis"
                    tokenizer, config, model = load_sentiment_model(model_path)
                    results = df["review"].apply(lambda x: predict_sentiment_multilingual(x, tokenizer, model))
                    df[["Dominant Sentiment", "Confidence"]] = results.values.tolist()
                    positions = df["Dominant Sentiment"].apply(sentiment_to_position_5)
                    overall_score = np.average(positions, weights=df["Confidence"] / 100)
                    if overall_score < 20:
                        overall_label = "Very Negative"
                    elif overall_score < 40:
                        overall_label = "Negative"
                    elif overall_score < 60:
                        overall_label = "Neutral"
                    elif overall_score < 80:
                        overall_label = "Positive"
                    else:
                        overall_label = "Very Positive"
                    segments = None  # Use default 5-segment scheme
                st.session_state.analyzed_df = df
                st.session_state.overall_score = overall_score
                st.session_state.overall_label = overall_label

        # Later, display analyzed data and the overall chart if available.
        if st.session_state.analyzed_df is not None:
            st.subheader("Analyzed Reviews")
            st.dataframe(st.session_state.analyzed_df)
            st.subheader("Overall Sentiment Score")
            st.markdown(f"**Overall Score: {st.session_state.overall_score:.2f}** ({st.session_state.overall_label})")
            # For Vietnamese model, use custom segments; otherwise, None.
            segments_to_use = segments if st.session_state.model_option == "Vietnamese" else None
            fig = plot_sentiment_scale(
                st.session_state.overall_score, st.session_state.overall_label,
                sentiment_score=st.session_state.overall_score,
                segments=segments_to_use
            )
            st.plotly_chart(fig, use_container_width=True, key="overall_chart")

def use_crawl_news():
    # Enhanced news crawler interface
    crawler_mode = st.radio(
        "Choose Crawler Mode:",
        ["🚀 Enhanced Multi-Source Crawler (Recommended)", "📊 Basic Single-Source Crawler"],
        horizontal=True,
        help="Enhanced mode supports multiple news sources with advanced features"
    )

    if crawler_mode == "🚀 Enhanced Multi-Source Crawler (Recommended)":
        from enhanced_crawl_news import create_enhanced_news_interface
        create_enhanced_news_interface()
    else:
        # Original basic crawler
        use_basic_crawl_news()

def use_basic_crawl_news():
    """Original basic news crawler functionality"""
    st.title("📰 Basic News Crawler")

    if "data" not in st.session_state:
        st.session_state.data = None

    if st.sidebar.button("Get Data"):
        with st.spinner("Crawling articles..."):
            data = run_extraction()
            if data:
                st.session_state.data = data
                st.success("Data extraction complete!")
            else:
                st.warning("No data found.")

    # Display results
    if st.session_state.data:
        df = pd.DataFrame(st.session_state.data)
        st.write(df)

        csv = df.to_csv(index=False)
        st.download_button(
            label="Download Data as CSV",
            data=csv,
            file_name="news_articles.csv",
            mime="text/csv",
        )

def use_stock_analysis():

    from vnstock import Vnstock
    import datetime
    import requests

    try:
        stock = Vnstock().stock(symbol='VND', source='TCBS')
    except requests.exceptions.JSONDecodeError as e:
        st.error(f"Error decoding JSON from API: {e}")
        st.error("Please check the API or your network connection.")
    except Exception as e:
        error_message = str(e)
        if "source" in error_message.lower():  # Check if the error is related to 'source'
            st.warning("TCBS source failed. Retrying with VCI...")
            try:
                stock = Vnstock().stock(symbol='VND', source='VCI')
            except Exception as e2:
                st.error(f"Failed with VCI as well: {e2}")
                stock = None
        else:
            st.error(f"An unexpected error occurred: {e}")
            stock = None

    from streamlit_option_menu import option_menu 
    page = option_menu(None, ["Market", "Single"], 
        icons=['boxes', 'box'], 
        menu_icon="cast", default_index=0, orientation="horizontal",
        styles={
            "container": {"padding": "0!important", "background-color": "#ffffff"},
            "icon": {"color": "#fcc603", "font-size": "25px"}, 
            "nav-link": {"font-size": "25px", "text-align": "left", "margin":"0px", "--hover-color": "#eee"},
            "nav-link-selected": {"background-color": "#69bd68"},
        }
    )
    if page == 'Single':
        
        st.sidebar.title("Stock Data")
        today = datetime.datetime.now()
        stock_symbol = st.sidebar.selectbox("Stock Symbol", stock.listing.all_symbols()['symbol'].sort_values())
        start_date = st.sidebar.date_input("Start Date", datetime.date(2024, 1, 1),format='YYYY-MM-DD')
        start_date = start_date.strftime('%Y-%m-%d')
        end_date = st.sidebar.date_input("End Date", today, format='YYYY-MM-DD')
        end_date = end_date.strftime('%Y-%m-%d')

        if st.sidebar.button('Get Data'):
            stock_df = stock.quote.history(symbol=stock_symbol, start=start_date, end=end_date)
            stock_df['time'] = stock_df['time'].apply(lambda x: x.strftime('%d/%m/%Y'))
            # Get news data from all Vietstock subdomains
            news_df = get_all_vietstock_data(
                symbol=stock_symbol,
                start_date=start_date,
                end_date=end_date
            )
            # Merge the data
            merged_df = merge_stock_with_all_news(stock_df, news_df)
            st.markdown("<h4 style='text-align: center; font-size: 20px; background-image: linear-gradient(to right, #96d9a4, #c23640); color:#061c04;'>"
                        "Stock data with news</h4>", unsafe_allow_html=True)  
            st.dataframe(merged_df, hide_index=True, width=2000)

            import plotly.graph_objects as go
            fig = go.Figure(data=[go.Candlestick(
                x=merged_df['time'],
                open=merged_df['open'],
                high=merged_df['high'],
                low=merged_df['low'],
                close=merged_df['close']
            )])

            # Customize the x-axis to show only months
            fig.update_layout(
                xaxis=dict(
                    tickformat='%b %Y',  # Format: Month Year (e.g., Jan 2025)
                    tickmode='auto',
                    nticks=12,  # Adjust this number to control how many tick marks to show
                )
            )

            st.plotly_chart(fig, use_container_width=True)

            # # Calculate daily price fluctuation
            # merged_df["price_fluctuation"] = merged_df["close"].diff()
            # # Forward fill news and stakeholder events to associate each day with the latest event
            # merged_df["latest_news_newest_news"] = merged_df["news_newest_news"].replace("", None).ffill()
            # merged_df["latest_news_business_results"] = merged_df["news_business_results"].replace("", None).ffill()
            # merged_df["latest_news_dividend"] = merged_df["news_dividend"].replace("", None).ffill()
            # merged_df["latest_news_internal_stakeholders"] = merged_df["news_internal_stakeholders"].replace("", None).ffill()
            # merged_df["latest_news_personnel"] = merged_df["news_personnel"].replace("", None).ffill()
            # merged_df["price_fluctuation_pct"] = merged_df["price_fluctuation"] / merged_df["close"].shift(1) * 100
            # st.dataframe(merged_df, hide_index=True)

            from datetime import datetime
            # Function to transform DataFrame
            def transform_dataframe(df):
                events = []
                
                for _, row in df.iterrows():
                    if not row["all_news"]:  # Skip empty events
                        continue  

                    # Convert date format
                    date_obj = datetime.strptime(row["time"], "%d/%m/%Y")

                    # Ensure all_news is a string (join list items if necessary)
                    if isinstance(row["all_news"], list):
                        formatted_news = "<br>".join(row["all_news"])  # Join list elements with line breaks
                    else:
                        formatted_news = str(row["all_news"]).replace(",", "<br>")  # Handle string case

                    event = {
                        "text": {
                            "headline": "Events of the day",  # Customize if needed
                            "text": formatted_news  # Use formatted text
                        },
                        "start_date": {
                            "year": str(date_obj.year),
                            "month": str(date_obj.month),
                            "day": str(date_obj.day)
                        }
                    }
                    events.append(event)

                return {"events": events}

            # Transform and save the result
            transformed_data = transform_dataframe(merged_df)

            # Save to JSON file
            with open("output.json", "w", encoding="utf-8") as f:
                json.dump(transformed_data, f, ensure_ascii=False, indent=2)

            import streamlit.components.v1 as components
            # parameters
            CDN_PATH = 'https://cdn.knightlab.com/libs/timeline3/latest'
            JSON_PATH = 'output.json'

            TL_HEIGHT = 450  # px

            # load data
            with open(JSON_PATH, "r") as f:
                json_text = f.read()
                source_param = 'timeline_json'
                source_block = f'var {source_param} = {json_text};'

            # load css + js
            css_block = f'<link title="timeline-styles" rel="stylesheet" href="{CDN_PATH}/css/timeline.css">'
            js_block = f'<script src="{CDN_PATH}/js/timeline.js"></script>'

            # write html block
            htmlcode = css_block + ''' 
            ''' + js_block + '''
                <div id='timeline-embed' style="width: 95%; height: ''' + str(TL_HEIGHT) + '''px; margin: 1px;"></div>
                <script type="text/javascript">
                    var additionalOptions = {
                        start_at_end: false, is_embed: true,
                    }
                    ''' + source_block + '''
                    timeline = new TL.Timeline('timeline-embed', ''' + source_param + ''', additionalOptions);
                </script>'''

            components.html(htmlcode, height=TL_HEIGHT)

            # EXPANDERS - Enhanced Analysis
            df = merged_df.copy()
            analyze_stock(df, symbol=stock_symbol, news_data=merged_df)
            
            with st.expander('Other metrics'):
                try:
                    # Get stock data
                    stock_df_copied = stock_df[['time', 'close']].rename(columns={'close': stock_symbol})
                    stock_df_copied['time'] = pd.to_datetime(stock_df_copied['time'], errors='coerce').dt.strftime('%Y-%m-%d')
                    
                    # Get VNINDEX data
                    try:
                        vnindex = stock.quote.history(symbol='VNINDEX', start=start_date, end=end_date)
                        # Check if VNINDEX data was retrieved successfully
                        if vnindex.empty:
                            st.warning("Could not retrieve VNINDEX data for the selected time period")
                            raise ValueError("Empty VNINDEX data")
                            
                        vnindex = vnindex[['time', 'close']].rename(columns={'close': 'VNINDEX'})
                        vnindex['time'] = pd.to_datetime(vnindex['time'], errors='coerce').dt.strftime('%Y-%m-%d')
                    except Exception as e:
                        st.error(f"Error fetching VNINDEX data: {str(e)}")
                        st.info("Beta calculation requires VNINDEX data for comparison")
                        raise
                    
                    # Merge data
                    final_df = pd.merge(stock_df_copied, vnindex, on='time', how='inner').set_index('time')
                    
                    # Check if merge resulted in data
                    if final_df.empty:
                        st.warning("No overlapping dates between stock data and VNINDEX")
                        raise ValueError("No overlapping data")
                        
                    # Reverse order
                    final_df = final_df.iloc[::-1]
                    
                    # Display original dataset
                    col1, col2 = st.columns(2)
                    with col1:
                        st.subheader("Original Dataset")
                        st.dataframe(final_df)
                    
                    # Define daily return function with proper error handling
                    def daily_return(df):
                        if df.empty:
                            return pd.DataFrame()
                            
                        # Calculate percent change
                        df_daily = df.pct_change() * 100
                        
                        # Only set the first row to 0 if the DataFrame has rows
                        if len(df_daily) > 0:
                            df_daily.iloc[0] = 0
                            
                        return df_daily
                    
                    # Calculate daily returns
                    data_daily_return = daily_return(final_df)
                    
                    # Display daily returns
                    with col2:
                        st.subheader("Daily Return Dataset")
                        st.dataframe(data_daily_return)
                    
                    # Define beta calculation with error handling
                    def calculate_beta(data_daily_return, stock_col):
                        try:
                            # Verify columns exist
                            if 'VNINDEX' not in data_daily_return.columns:
                                st.warning("VNINDEX column not found in data")
                                return 0, 0
                                
                            if stock_col not in data_daily_return.columns:
                                st.warning(f"{stock_col} column not found in data")
                                return 0, 0
                            
                            # Drop NaN values for clean calculation
                            valid_data = data_daily_return[['VNINDEX', stock_col]].dropna()
                            
                            if len(valid_data) < 2:
                                st.warning("Not enough valid data points to calculate beta")
                                return 0, 0
                            
                            # Calculate beta using linear regression
                            beta, alpha = np.polyfit(valid_data['VNINDEX'], valid_data[stock_col], 1)
                            return beta, alpha
                        except Exception as e:
                            st.error(f"Error calculating beta: {str(e)}")
                            return 0, 0
                    
                    # Calculate beta
                    beta, alpha = calculate_beta(data_daily_return, stock_symbol)
                    
                    # Display beta
                    with col1:
                        st.subheader("Beta Value")
                        if beta != 0:
                            st.write(f"Beta for {stock_symbol}: {round(beta, 2)}")
                        else:
                            st.write("Could not calculate beta")
                    
                    # Only calculate CAPM if we have a valid beta
                    if beta != 0:
                        # Calculate CAPM expected return
                        rf = 0  # Risk-free rate
                        rm = data_daily_return['VNINDEX'].mean() * 252  # Market return (annualized)
                        expected_return = rf + beta * (rm - rf)
                        
                        # Display CAPM expected return
                        with col2:
                            st.subheader("CAPM Expected Return")
                            st.write(f"Expected return for {stock_symbol}: {round(expected_return, 2)}%")
                            
                except Exception as e:
                    st.error(f"Error in metrics calculation: {str(e)}")
                    st.info("Please check your data and try again")
    
    elif page == 'Market':
        from market_analysis import run_market_analysis
        run_market_analysis()
           
                
def use_crawl_jobs():
    st.title("Job Crawler")
    st.write("This feature is under development.")
    st.info("Coming soon: Job listings crawler and analyzer")

def use_admin_dashboard():
    """Admin dashboard page function"""
    # Track page visit
    track_page_visit("Admin Dashboard")

    # Create admin dashboard
    create_admin_dashboard()

import os
def initialize_auth_config():
    """Create or initialize authentication config file"""
    config_path = 'config.yaml'
    if not os.path.exists(config_path):
        # Create default config
        default_config = {
            'cookie': {
                'expiry_days': 30,
                'key': 'some_signature_key',
                'name': 'admin_auth_cookie'
            },
            'credentials': {
                'usernames': {
                    'admin': {
                        'email': '<EMAIL>',
                        'failed_login_attempts': 0,
                        'first_name': 'Admin',
                        'last_name': 'User',
                        'logged_in': False,
                        'password': 'admin123',  # Will be hashed automatically
                        'roles': ['admin']
                    }
                }
            },
            'pre-authorized': {
                'emails': ['<EMAIL>']
            }
        }
        
        # Save config to file
        with open(config_path, 'w') as file:
            yaml.dump(default_config, file, default_flow_style=False)
            
    # Load config
    with open(config_path) as file:
        config = yaml.load(file, Loader=yaml.SafeLoader)
    
    return config

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
def send_password_email(email_address, username, new_password):
    """Sends the new password to the user's email."""
    # Email configuration
    sender_email = "<EMAIL>"
    sender_password = "eanc iuge ocol jeir" 

    message = MIMEMultipart("alternative")
    message["Subject"] = "Mật khẩu mới của bạn"
    message["From"] = sender_email
    message["To"] = email_address

    # Create the plain-text and HTML version of your message
    text = f"""
    Chào {username},

    Mật khẩu mới của bạn là: {new_password}

    Vui lòng đổi mật khẩu sau khi đăng nhập.
    """
    html = f"""
    <html>
      <body>
        <p>Chào {username},<br><br>
           Mật khẩu mới của bạn là: <b>{new_password}</b><br><br>
           Vui lòng đổi mật khẩu sau khi đăng nhập.
        </p>
      </body>
    </html>
    """

    # Turn these into plain/html MIMEText objects
    part1 = MIMEText(text, "plain")
    part2 = MIMEText(html, "html")

    # Add HTML/plain-text parts to MIMEMultipart message
    # The email client will try to render the last part first
    message.attach(part1)
    message.attach(part2)

    context = ssl.create_default_context()
    try:
        with smtplib.SMTP_SSL("smtp.gmail.com", 465, context=context) as server:
            server.login(sender_email, sender_password)
            server.sendmail(sender_email, email_address, message.as_string())
        # st.success(f"Mật khẩu mới đã được gửi đến {email_address}")
    except Exception as e:
        st.error(f"Lỗi khi gửi email: {e}")

def main():
    st.set_page_config(
        page_title="Event Registration System",
        page_icon="📊",
        layout="wide"
    )

    UI()

    # Đảm bảo hàm display_registration_and_password_recovery được định nghĩa trước khi sử dụng
    auth_config = initialize_auth_config()
    
    # Khởi tạo authenticator
    authenticator = stauth.Authenticate(
        auth_config['credentials'],
        auth_config['cookie']['name'],
        auth_config['cookie']['key'],
        auth_config['cookie']['expiry_days']
    )
    
    # Lấy trạng thái xác thực từ session state
    auth_status = st.session_state.get('authentication_status')
    
    # Kiểm tra xem người dùng có vừa đăng xuất hay không
    if 'logout_clicked' in st.session_state and st.session_state.logout_clicked:
        st.session_state.logout_clicked = False
        # Xóa thông tin sidebar từ session state
        if 'sidebar_state' in st.session_state:
            del st.session_state['sidebar_state']
        st.rerun()  # Tải lại trang sau khi đăng xuất
    
    # Ẩn sidebar mặc định nếu chưa đăng nhập
    if not auth_status:
        # Sử dụng CSS để ẩn sidebar khi chưa đăng nhập
        st.markdown("""
        <style>
        [data-testid="stSidebar"] {
            display: none;
        }
        </style>
        """, unsafe_allow_html=True)
    
    # Hiển thị nội dung dựa vào trạng thái xác thực
    if auth_status:
        # Lưu trạng thái hiển thị sidebar trong session state
        st.session_state['sidebar_state'] = 'visible'
        
        # Hiển thị sidebar với nút đăng xuất
        st.sidebar.write(f'Xin chào, *{st.session_state.get("name")}*')
        
        # Initialize user session tracking
        initialize_user_session()

        # Tùy chỉnh nút đăng xuất để theo dõi khi được nhấn
        if authenticator.logout('Đăng xuất', 'sidebar'):
            st.session_state.logout_clicked = True
            st.rerun()  # Tải lại trang ngay lập tức sau khi đăng xuất

        # Check if user is admin
        is_admin = AdminAuth.is_admin_user(st.session_state.get('username', ''))

        # Định nghĩa trang điều hướng
        pages = [
            st.Page(use_general_search, title="Công cụ AI-powered Search", icon="🔥"),
            st.Page(use_review_analysis, title="Cào và phân tích phản hồi của App", icon="🔥"),
            st.Page(use_crawl_news, title="Cào và tổng hợp nius mới nhất", icon="⚙️"),
            st.Page(use_stock_analysis, title="Công cụ thu thập, phân tích và khuyến nghị Sờ Tóc", icon="🔥"),
            st.Page(use_crawl_jobs, title="Cào và tổng hợp gióps mới nhất", icon="⚙️"),
        ]

        # Add admin page if user is admin
        if is_admin:
            pages.append(st.Page(use_admin_dashboard, title="🔧 Admin Dashboard", icon="⚙️"))

        pg = st.navigation(pages)

        st.sidebar.markdown("---")
        pg.run()

        # Add support chat widget to all pages
        create_support_chat_widget()
        
    elif auth_status is False:
        st.error('Tên đăng nhập/mật khẩu không chính xác')
        authenticator.login()
        
        # Vẫn hiển thị phần đăng ký và quên mật khẩu ngay cả khi đăng nhập thất bại
        display_registration_and_password_recovery(authenticator, auth_config)
    else:
        st.subheader("Đăng nhập hệ thống")
        authenticator.login()
        
        # Hiển thị phần đăng ký và quên mật khẩu
        display_registration_and_password_recovery(authenticator, auth_config)

# Tách hàm hiển thị phần đăng ký và quên mật khẩu để tái sử dụng dễ dàng
def display_registration_and_password_recovery(authenticator, auth_config):
    st.markdown("---")

    st.subheader("Đăng ký tài khoản quản trị mới")
    with st.expander("Click here"):
        # # Phần đăng ký
        # st.subheader("Đăng ký tài khoản quản trị mới")
        try:
            email_of_registered_user, username_of_registered_user, name_of_registered_user = authenticator.register_user(
                # pre_authorized=auth_config.get('pre-authorized', {}).get('emails', [])
            )
            if email_of_registered_user:
                st.success('Đăng ký tài khoản thành công')
                # Cập nhật file config
                with open('config.yaml', 'w') as file:
                    yaml.dump(auth_config, file, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            st.error(e)

    st.markdown("---")
    st.subheader("Quên mật khẩu?")
    with st.expander("Click here"):
        # Phần quên mật khẩu
        # st.subheader("Quên mật khẩu?")
        try:
            username_of_forgotten_password, email_of_forgotten_password, new_random_password = authenticator.forgot_password()
            if username_of_forgotten_password:
                send_password_email(email_of_forgotten_password, username_of_forgotten_password, new_random_password)
                st.success(f'Mật khẩu mới đã được gửi đến email của bạn')
                # Cập nhật file config
                with open('config.yaml', 'w') as file:
                    yaml.dump(auth_config, file, default_flow_style=False, allow_unicode=True)
            elif username_of_forgotten_password == False:
                st.error(f'Không tìm thấy tên người dùng')
        except Exception as e:
            st.error(e)
    
    # Cập nhật config mỗi khi trạng thái xác thực thay đổi
    with open('config.yaml', 'w') as file:
        yaml.dump(auth_config, file, default_flow_style=False, allow_unicode=True)

if __name__ == "__main__":
    main()



