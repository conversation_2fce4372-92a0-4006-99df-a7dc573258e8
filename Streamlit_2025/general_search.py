from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON>rawler, CacheMode, CrawlerRunConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
import asyncio
import json
import streamlit as st

async def crawl_google_results(keyword: str, pages: int = 3): # default page = 3
    search_schema = {
        "name": "SearchResults",
        "baseSelector": "div.g",
        "fields": [
            {"name": "title", "selector": "h3", "type": "text"},
            {"name": "url", "selector": "a[href^='http']", "type": "attribute", "attribute": "href"},
            {"name": "description", "selector": "div.VwiC3b", "type": "text"}
        ]
    }
    
    extraction_strategy = JsonCssExtractionStrategy(search_schema)
    collected_data = []

    async with AsyncWebCrawler(
        headless=True,
        verbose=True,
        user_agent_mode="random"
    ) as crawler:
        
        for page in range(pages):
            try:
                start = page * 10  # Google paginates every 10 results
                search_url = f"https://www.google.com/search?q={keyword}&num=30&start={start}"

                search_config = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    extraction_strategy=extraction_strategy,
                    delay_before_return_html=3,  # Reduced wait time
                    screenshot=False,
                    page_timeout=30000,  # 30 second timeout instead of 60
                    js_code="""
                        async () => {
                            // Wait for page to load
                            await new Promise(r => setTimeout(r, 2000));

                            // Scroll to load more results
                            for (let i = 0; i < 2; i++) {
                                window.scrollTo(0, document.body.scrollHeight);
                                await new Promise(r => setTimeout(r, 1000));
                            }
                            window.scrollTo(0, 0);

                            // Wait a bit more for results to stabilize
                            await new Promise(r => setTimeout(r, 1000));
                        }
                    """,
                    # Remove wait_for to avoid timeout issues
                    # wait_for="css:div.g"
                )

                search_result = await crawler.arun(url=search_url, config=search_config)

            except Exception as e:
                st.warning(f"Error crawling page {page + 1}: {str(e)}")
                continue  # Skip this page and continue with next

            if search_result.success and search_result.extracted_content:
                try:
                    search_items = json.loads(search_result.extracted_content)
                except json.JSONDecodeError:
                    st.error("Error: Unable to parse search results JSON")
                    continue
                
                for item in search_items:
                    if "url" in item and item["url"].startswith("http"):
                        collected_data.append({
                            "title": item.get("title", ""),
                            "url": item["url"],
                            "description": item.get("description", "")
                        })
                        st.write(f"Extracted: {item['url']}")
            
            await asyncio.sleep(3)  # Avoid rate-limiting

    return collected_data