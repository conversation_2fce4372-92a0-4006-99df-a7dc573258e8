"""
Job Search Interface
===================

Complete job search interface with crawling, AI matching, and CV analysis.
"""

import streamlit as st
import pandas as pd
import asyncio
import json
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from typing import List, Dict

from job_crawler import search_jobs, get_supported_job_sites, format_job_for_display
from job_ai_matcher import ai_matcher, setup_google_ai_key, check_dependencies

def create_job_search_interface():
    """Create the main job search interface"""
    
    st.title("🚀 AI-Powered Job Search for Vietnam Market")
    st.markdown("Find the perfect job using AI-powered matching and comprehensive job crawling")
    
    # Check dependencies
    if not check_dependencies():
        st.stop()
    
    # Create tabs for different functionalities
    tab1, tab2, tab3, tab4 = st.tabs([
        "🔍 Job Search", 
        "🤖 AI Job Matching", 
        "📊 Job Analytics", 
        "⚙️ Settings"
    ])
    
    with tab1:
        create_job_search_tab()
    
    with tab2:
        create_ai_matching_tab()
    
    with tab3:
        create_job_analytics_tab()
    
    with tab4:
        create_settings_tab()

def create_job_search_tab():
    """Create job search tab"""
    
    st.header("🔍 Search Jobs")
    
    # Search form
    with st.form("job_search_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            keyword = st.text_input(
                "Job Keyword *", 
                placeholder="e.g., Python Developer, Marketing Manager",
                help="Enter job title, skills, or keywords"
            )
            
            location = st.text_input(
                "Location", 
                placeholder="e.g., Ho Chi Minh City, Hanoi",
                help="Preferred work location"
            )
        
        with col2:
            domain = st.selectbox(
                "Industry Domain",
                ["", "IT/Software", "Marketing", "Finance", "Sales", "HR", "Engineering", "Healthcare", "Education", "Other"],
                help="Select industry preference"
            )
            
            max_pages = st.slider(
                "Pages per site", 
                min_value=1, 
                max_value=5, 
                value=2,
                help="More pages = more results but slower"
            )
        
        # Search button
        search_clicked = st.form_submit_button("🚀 Search Jobs", type="primary")
    
    # Display supported job sites
    st.info(f"🌐 Searching across: {', '.join([site.title() for site in get_supported_job_sites()])}")
    
    # Perform search
    if search_clicked:
        if not keyword.strip():
            st.error("Please enter a job keyword")
            return
        
        with st.spinner(f"🔍 Searching for '{keyword}' jobs..."):
            try:
                # Try main search first
                jobs = asyncio.run(search_jobs(
                    keyword=keyword,
                    location=location,
                    domain=domain,
                    max_pages_per_site=max_pages
                ))

                # If no relevant jobs found, try simple search
                if not jobs or len(jobs) < 3:
                    st.info("🔄 Trying alternative search method...")
                    try:
                        from simple_job_search import simple_job_search
                        simple_jobs = simple_job_search(keyword, location)

                        if simple_jobs:
                            jobs.extend(simple_jobs)
                            st.info(f"✅ Found {len(simple_jobs)} additional relevant jobs")
                    except Exception as e:
                        print(f"Simple search fallback failed: {e}")

                if jobs:
                    st.session_state.search_results = jobs
                    st.session_state.search_query = {
                        'keyword': keyword,
                        'location': location,
                        'domain': domain,
                        'timestamp': datetime.now().isoformat()
                    }
                    st.success(f"✅ Found {len(jobs)} jobs!")

                    # Show relevance info
                    relevant_count = sum(1 for job in jobs if keyword.lower() in job.get('title', '').lower() or
                                       keyword.lower() in job.get('description', '').lower())
                    if relevant_count > 0:
                        st.info(f"🎯 {relevant_count} jobs are highly relevant to '{keyword}'")
                else:
                    st.warning("No jobs found. Try different keywords or locations.")
                    st.info("💡 Try broader terms like 'developer', 'analyst', or 'manager'")

            except Exception as e:
                st.error(f"Search failed: {str(e)}")
                st.info("💡 Trying simple search as fallback...")

                # Fallback to simple search
                try:
                    from simple_job_search import simple_job_search
                    jobs = simple_job_search(keyword, location)

                    if jobs:
                        st.session_state.search_results = jobs
                        st.session_state.search_query = {
                            'keyword': keyword,
                            'location': location,
                            'domain': domain,
                            'timestamp': datetime.now().isoformat()
                        }
                        st.success(f"✅ Found {len(jobs)} jobs using fallback search!")
                    else:
                        st.error("All search methods failed. Please try again later.")
                except Exception as fallback_error:
                    st.error(f"Fallback search also failed: {fallback_error}")
    
    # Display search results
    if 'search_results' in st.session_state and st.session_state.search_results:
        display_job_results(st.session_state.search_results)

def create_ai_matching_tab():
    """Create AI job matching tab"""
    
    st.header("🤖 AI-Powered Job Matching")
    
    # Setup Google AI
    if not setup_google_ai_key():
        st.stop()
    
    # CV Upload section
    st.subheader("📄 Upload Your CV")
    
    uploaded_file = st.file_uploader(
        "Choose your CV file",
        type=['pdf', 'docx', 'txt'],
        help="Upload your CV in PDF, DOCX, or TXT format"
    )
    
    if uploaded_file:
        with st.spinner("📖 Analyzing your CV..."):
            try:
                # Extract text from CV
                cv_text = ai_matcher.extract_text_from_file(uploaded_file)
                
                if "Error" in cv_text:
                    st.error(cv_text)
                    return
                
                # Analyze CV with AI
                cv_analysis = ai_matcher.analyze_cv_with_ai(cv_text)
                
                if "error" in cv_analysis:
                    st.error(f"CV analysis failed: {cv_analysis['error']}")
                    return
                
                # Store CV analysis
                st.session_state.cv_analysis = cv_analysis
                st.session_state.cv_text = cv_text
                
                # Display CV analysis
                display_cv_analysis(cv_analysis)
                
            except Exception as e:
                st.error(f"Error processing CV: {str(e)}")
    
    # Job matching section
    if 'cv_analysis' in st.session_state and 'search_results' in st.session_state:
        st.subheader("🎯 AI Job Matching")
        
        if st.button("🤖 Find Best Matches", type="primary"):
            with st.spinner("🧠 AI is analyzing job matches..."):
                try:
                    ranked_jobs = ai_matcher.rank_jobs_for_cv(
                        st.session_state.cv_analysis,
                        st.session_state.search_results
                    )
                    
                    st.session_state.ranked_jobs = ranked_jobs
                    st.success(f"✅ Ranked {len(ranked_jobs)} jobs by match score!")
                    
                except Exception as e:
                    st.error(f"Job matching failed: {str(e)}")
        
        # Display ranked results
        if 'ranked_jobs' in st.session_state:
            display_ranked_jobs(st.session_state.ranked_jobs)
    
    elif 'cv_analysis' in st.session_state:
        st.info("💡 Upload your CV and search for jobs to see AI-powered matches!")
    else:
        st.info("💡 Please upload your CV first, then search for jobs in the Search tab.")

def create_job_analytics_tab():
    """Create job analytics tab"""
    
    st.header("📊 Job Market Analytics")
    
    if 'search_results' not in st.session_state or not st.session_state.search_results:
        st.info("💡 Search for jobs first to see analytics")
        return
    
    jobs = st.session_state.search_results
    df = pd.DataFrame(jobs)
    
    # Basic statistics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Jobs", len(jobs))
    
    with col2:
        unique_companies = df['company'].nunique() if 'company' in df.columns else 0
        st.metric("Companies", unique_companies)
    
    with col3:
        unique_locations = df['location'].nunique() if 'location' in df.columns else 0
        st.metric("Locations", unique_locations)
    
    with col4:
        job_sources = df['source'].nunique() if 'source' in df.columns else 0
        st.metric("Job Sites", job_sources)
    
    # Charts
    if len(df) > 0:
        col1, col2 = st.columns(2)
        
        with col1:
            # Jobs by source
            if 'source' in df.columns:
                source_counts = df['source'].value_counts()
                fig_source = px.pie(
                    values=source_counts.values,
                    names=source_counts.index,
                    title="Jobs by Source"
                )
                st.plotly_chart(fig_source, use_container_width=True)
        
        with col2:
            # Top locations
            if 'location' in df.columns:
                location_counts = df['location'].value_counts().head(10)
                fig_location = px.bar(
                    x=location_counts.values,
                    y=location_counts.index,
                    orientation='h',
                    title="Top Locations"
                )
                fig_location.update_layout(yaxis={'categoryorder': 'total ascending'})
                st.plotly_chart(fig_location, use_container_width=True)
        
        # Top companies
        if 'company' in df.columns:
            st.subheader("🏢 Top Hiring Companies")
            company_counts = df['company'].value_counts().head(15)
            
            fig_companies = px.bar(
                x=company_counts.index,
                y=company_counts.values,
                title="Companies with Most Job Postings"
            )
            fig_companies.update_layout(xaxis_tickangle=-45)
            st.plotly_chart(fig_companies, use_container_width=True)

def create_settings_tab():
    """Create settings tab"""
    
    st.header("⚙️ Settings")
    
    # Google AI settings
    st.subheader("🤖 AI Configuration")
    setup_google_ai_key()
    
    # Export settings
    st.subheader("📤 Export Data")
    
    if 'search_results' in st.session_state:
        jobs_df = pd.DataFrame(st.session_state.search_results)
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Export as CSV
            csv = jobs_df.to_csv(index=False)
            st.download_button(
                label="📊 Download as CSV",
                data=csv,
                file_name=f"job_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
        
        with col2:
            # Export as JSON
            json_data = json.dumps(st.session_state.search_results, indent=2)
            st.download_button(
                label="📋 Download as JSON",
                data=json_data,
                file_name=f"job_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
    
    # Clear data
    st.subheader("🗑️ Clear Data")
    if st.button("Clear All Search Data", type="secondary"):
        keys_to_clear = ['search_results', 'cv_analysis', 'ranked_jobs', 'search_query']
        for key in keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]
        st.success("✅ All data cleared!")
        st.rerun()

def display_job_results(jobs: List[Dict]):
    """Display job search results"""
    
    st.subheader(f"📋 Found {len(jobs)} Jobs")
    
    # Filter options
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Filter by source
        sources = list(set([job.get('source', 'Unknown') for job in jobs]))
        selected_sources = st.multiselect("Filter by Source", sources, default=sources)
    
    with col2:
        # Filter by location
        locations = list(set([job.get('location', 'Unknown') for job in jobs if job.get('location')]))
        selected_locations = st.multiselect("Filter by Location", locations[:10])  # Limit to top 10
    
    with col3:
        # Sort options
        sort_by = st.selectbox("Sort by", ["Relevance", "Company", "Location", "Source"])
    
    # Apply filters
    filtered_jobs = jobs
    if selected_sources:
        filtered_jobs = [job for job in filtered_jobs if job.get('source') in selected_sources]
    if selected_locations:
        filtered_jobs = [job for job in filtered_jobs if job.get('location') in selected_locations]
    
    # Display jobs
    for i, job in enumerate(filtered_jobs):
        with st.expander(f"🔹 {job.get('title', 'Unknown Title')} - {job.get('company', 'Unknown Company')}"):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**Company:** {job.get('company', 'N/A')}")
                st.write(f"**Location:** {job.get('location', 'N/A')}")
                st.write(f"**Salary:** {job.get('salary', 'Not specified')}")
                st.write(f"**Source:** {job.get('source', 'N/A').title()}")
                
                if job.get('description'):
                    st.write("**Description:**")
                    st.write(job['description'][:300] + "..." if len(job['description']) > 300 else job['description'])
            
            with col2:
                if job.get('url'):
                    st.link_button("🔗 View Job", job['url'])
                
                # Show match score if available
                if 'match_score' in job:
                    score = job['match_score'] * 100
                    st.metric("AI Match Score", f"{score:.1f}%")

def display_cv_analysis(cv_analysis: Dict):
    """Display CV analysis results"""
    
    st.subheader("📊 CV Analysis Results")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Skills:**")
        skills = cv_analysis.get('skills', [])
        if skills:
            for skill in skills[:10]:  # Show top 10 skills
                st.write(f"• {skill}")
        else:
            st.write("No skills detected")
        
        st.write("**Experience:**")
        experience = cv_analysis.get('experience_years', 'Unknown')
        st.write(f"{experience} years")
    
    with col2:
        st.write("**Previous Roles:**")
        job_titles = cv_analysis.get('job_titles', [])
        if job_titles:
            for title in job_titles[:5]:  # Show top 5 titles
                st.write(f"• {title}")
        else:
            st.write("No previous roles detected")
        
        st.write("**Industries:**")
        industries = cv_analysis.get('industries', [])
        if industries:
            for industry in industries[:5]:
                st.write(f"• {industry}")
        else:
            st.write("No industries detected")
    
    # Professional summary
    if cv_analysis.get('summary'):
        st.write("**Professional Summary:**")
        st.write(cv_analysis['summary'])

def display_ranked_jobs(ranked_jobs: List[Dict]):
    """Display AI-ranked job results"""
    
    st.subheader("🎯 AI-Ranked Job Matches")
    
    # Show top matches
    top_matches = ranked_jobs[:10]  # Show top 10 matches
    
    for i, job in enumerate(top_matches):
        match_score = job.get('match_score', 0) * 100
        match_analysis = job.get('match_analysis', {})
        
        # Color code based on match score
        if match_score >= 80:
            score_color = "🟢"
        elif match_score >= 60:
            score_color = "🟡"
        else:
            score_color = "🔴"
        
        with st.expander(f"{score_color} {match_score:.1f}% - {job.get('title', 'Unknown')} at {job.get('company', 'Unknown')}"):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                # Job details
                st.write(f"**Company:** {job.get('company', 'N/A')}")
                st.write(f"**Location:** {job.get('location', 'N/A')}")
                st.write(f"**Source:** {job.get('source', 'N/A').title()}")
                
                # AI analysis
                if 'reasoning' in match_analysis:
                    st.write("**AI Analysis:**")
                    st.write(match_analysis['reasoning'])
                
                if 'matching_skills' in match_analysis:
                    st.write("**Matching Skills:**")
                    st.write(", ".join(match_analysis['matching_skills']))
                
                if 'missing_skills' in match_analysis:
                    st.write("**Skills to Develop:**")
                    st.write(", ".join(match_analysis['missing_skills']))
            
            with col2:
                st.metric("Match Score", f"{match_score:.1f}%")
                
                if job.get('url'):
                    st.link_button("🔗 Apply Now", job['url'])
                
                # Generate application advice
                if st.button(f"💡 Get Advice", key=f"advice_{i}"):
                    with st.spinner("Generating advice..."):
                        advice = ai_matcher.generate_application_advice(
                            st.session_state.cv_analysis, job
                        )
                        st.write("**Application Advice:**")
                        st.write(advice)
