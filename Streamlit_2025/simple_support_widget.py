"""
Simple Support Widget that Always Works
=======================================

A simplified support widget that uses SQLite and always works,
providing persistent chat channels for users.
"""

import streamlit as st
import sqlite3
import pandas as pd
from datetime import datetime
import uuid
import os

class SimpleSupportSystem:
    """Simple support system using SQLite"""
    
    def __init__(self):
        self.db_path = "simple_support.db"
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tickets table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tickets (
                ticket_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                subject TEXT NOT NULL,
                description TEXT,
                status TEXT DEFAULT 'open',
                priority TEXT DEFAULT 'medium',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create messages table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                message_id INTEGER PRIMARY KEY AUTOINCREMENT,
                ticket_id TEXT NOT NULL,
                sender_id TEXT NOT NULL,
                sender_type TEXT NOT NULL,
                message TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (ticket_id) REFERENCES tickets (ticket_id)
            )
        """)
        
        conn.commit()
        conn.close()
    
    def create_ticket(self, user_id: str, subject: str, description: str, priority: str = 'medium') -> str:
        """Create a new support ticket"""
        ticket_id = f"TICKET-{int(datetime.now().timestamp())}-{str(uuid.uuid4())[:8]}"
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO tickets (ticket_id, user_id, subject, description, priority)
            VALUES (?, ?, ?, ?, ?)
        """, (ticket_id, user_id, subject, description, priority))
        
        # Add initial message
        cursor.execute("""
            INSERT INTO messages (ticket_id, sender_id, sender_type, message)
            VALUES (?, ?, ?, ?)
        """, (ticket_id, user_id, 'user', description))
        
        conn.commit()
        conn.close()
        
        return ticket_id
    
    def add_message(self, ticket_id: str, sender_id: str, sender_type: str, message: str) -> bool:
        """Add a message to a ticket"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO messages (ticket_id, sender_id, sender_type, message)
                VALUES (?, ?, ?, ?)
            """, (ticket_id, sender_id, sender_type, message))
            
            # Update ticket timestamp
            cursor.execute("""
                UPDATE tickets SET updated_at = CURRENT_TIMESTAMP
                WHERE ticket_id = ?
            """, (ticket_id,))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error adding message: {e}")
            return False
    
    def get_user_tickets(self, user_id: str) -> pd.DataFrame:
        """Get all tickets for a user"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT ticket_id, subject, description, status, priority, created_at, updated_at
            FROM tickets
            WHERE user_id = ? AND status != 'closed'
            ORDER BY updated_at DESC
        """
        
        df = pd.read_sql_query(query, conn, params=(user_id,))
        conn.close()
        
        return df
    
    def get_ticket_messages(self, ticket_id: str) -> pd.DataFrame:
        """Get all messages for a ticket"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT message_id, sender_id, sender_type, message, created_at
            FROM messages
            WHERE ticket_id = ?
            ORDER BY created_at ASC
        """
        
        df = pd.read_sql_query(query, conn, params=(ticket_id,))
        conn.close()
        
        return df
    
    def close_ticket(self, ticket_id: str, closer_id: str) -> bool:
        """Close a ticket"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE tickets SET status = 'closed', updated_at = CURRENT_TIMESTAMP
                WHERE ticket_id = ?
            """, (ticket_id,))
            
            # Add closing message
            cursor.execute("""
                INSERT INTO messages (ticket_id, sender_id, sender_type, message)
                VALUES (?, ?, ?, ?)
            """, (ticket_id, closer_id, 'system', 'Ticket has been closed'))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error closing ticket: {e}")
            return False

# Global support system
support_system = SimpleSupportSystem()

def create_simple_support_widget():
    """Create a simple support widget that always works"""
    
    # Initialize widget state
    if 'support_widget_open' not in st.session_state:
        st.session_state.support_widget_open = False
    if 'selected_ticket_id' not in st.session_state:
        st.session_state.selected_ticket_id = None
    
    # CSS for floating widget
    st.markdown("""
    <style>
    .support-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        font-family: 'Source Sans Pro', sans-serif;
        border: 1px solid #e0e0e0;
    }
    
    .support-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 50px;
        padding: 15px 25px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
    }
    
    .support-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Check authentication
    if not st.session_state.get('authentication_status'):
        # Show button but require login
        if st.button("💬 Need Help?", key="support_login_required", help="Login required for support"):
            st.warning("Please log in to use support chat")
        return
    
    user_id = st.session_state.get('username', 'anonymous')
    
    # Support widget
    if not st.session_state.support_widget_open:
        # Floating support button
        if st.button("💬 Need Help?", key="support_toggle", help="Click for support"):
            st.session_state.support_widget_open = True
            st.rerun()
    else:
        # Support window
        create_support_window(user_id)

def create_support_window(user_id: str):
    """Create the support window"""
    
    # Header
    col1, col2 = st.columns([4, 1])
    with col1:
        st.markdown("### 💬 Support Center")
    with col2:
        if st.button("✕", key="support_close", help="Close support"):
            st.session_state.support_widget_open = False
            st.session_state.selected_ticket_id = None
            st.rerun()
    
    # Get user's tickets
    tickets = support_system.get_user_tickets(user_id)
    
    if not tickets.empty:
        st.markdown("**Your Active Conversations:**")
        
        # Show tickets
        for _, ticket in tickets.iterrows():
            ticket_id = ticket['ticket_id']
            subject = ticket['subject']
            status = ticket['status']
            
            col1, col2, col3 = st.columns([3, 1, 1])
            
            with col1:
                if st.button(
                    f"📋 {subject[:30]}..." if len(subject) > 30 else f"📋 {subject}",
                    key=f"ticket_{ticket_id}",
                    type="primary" if st.session_state.selected_ticket_id == ticket_id else "secondary"
                ):
                    st.session_state.selected_ticket_id = ticket_id
                    st.rerun()
            
            with col2:
                st.caption(f"Status: {status}")
            
            with col3:
                if st.button("🗑️", key=f"close_{ticket_id}", help="Close ticket"):
                    support_system.close_ticket(ticket_id, user_id)
                    if st.session_state.selected_ticket_id == ticket_id:
                        st.session_state.selected_ticket_id = None
                    st.success("Ticket closed!")
                    st.rerun()
        
        st.divider()
    
    # Show selected ticket or new ticket form
    if st.session_state.selected_ticket_id:
        show_ticket_chat(st.session_state.selected_ticket_id, user_id)
    else:
        show_new_ticket_form(user_id)

def show_ticket_chat(ticket_id: str, user_id: str):
    """Show chat for selected ticket"""
    
    st.markdown(f"**Chat: {ticket_id}**")
    
    # Get messages
    messages = support_system.get_ticket_messages(ticket_id)
    
    if not messages.empty:
        for _, msg in messages.iterrows():
            sender_type = msg['sender_type']
            sender_id = msg['sender_id']
            content = msg['message']
            timestamp = msg['created_at']
            
            if sender_type == 'user':
                st.markdown(f"""
                <div style="text-align: right; margin-bottom: 10px;">
                    <div style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                               color: white; padding: 8px 12px; border-radius: 12px; max-width: 80%;">
                        <strong>You</strong><br>{content}
                        <div style="font-size: 10px; opacity: 0.8; margin-top: 4px;">{timestamp}</div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
            elif sender_type == 'admin':
                st.markdown(f"""
                <div style="text-align: left; margin-bottom: 10px;">
                    <div style="display: inline-block; background: #f5f5f5; color: #333; 
                               padding: 8px 12px; border-radius: 12px; max-width: 80%;">
                        <strong>🔧 Support</strong><br>{content}
                        <div style="font-size: 10px; opacity: 0.6; margin-top: 4px;">{timestamp}</div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
            else:  # system
                st.info(f"🔔 {content}")
    
    # Message input
    st.markdown("---")
    new_message = st.text_area(
        "Type your message:",
        placeholder="Type your reply here...",
        height=80,
        key=f"message_input_{ticket_id}"
    )
    
    # Send button
    col1, col2 = st.columns([2, 1])
    
    with col1:
        if st.button("📤 Send Message", key=f"send_{ticket_id}"):
            if new_message.strip():
                if support_system.add_message(ticket_id, user_id, 'user', new_message):
                    st.success("Message sent!")
                    st.rerun()
                else:
                    st.error("Failed to send message")
            else:
                st.error("Please enter a message")
    
    with col2:
        if st.button("🔄 Refresh", key=f"refresh_{ticket_id}"):
            st.rerun()

def show_new_ticket_form(user_id: str):
    """Show form to create new ticket"""
    
    st.markdown("**Start New Conversation:**")
    
    # Quick actions
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🐛 Report Bug", key="quick_bug"):
            st.session_state.quick_subject = "Bug Report"
            st.session_state.quick_message = "I found a bug in the application. Here are the details:"
    
    with col2:
        if st.button("❓ Ask Question", key="quick_question"):
            st.session_state.quick_subject = "Question"
            st.session_state.quick_message = "I have a question about how to use:"
    
    with col3:
        if st.button("💡 Feature Request", key="quick_feature"):
            st.session_state.quick_subject = "Feature Request"
            st.session_state.quick_message = "I would like to suggest a new feature:"
    
    # Form
    subject = st.text_input(
        "Subject:",
        value=st.session_state.get('quick_subject', ''),
        placeholder="Brief description of your issue",
        key="new_ticket_subject"
    )
    
    message = st.text_area(
        "Message:",
        value=st.session_state.get('quick_message', ''),
        placeholder="Describe your issue or question in detail...",
        height=100,
        key="new_ticket_message"
    )
    
    priority = st.selectbox(
        "Priority:",
        ["low", "medium", "high", "urgent"],
        index=1,
        key="new_ticket_priority"
    )
    
    # Create button
    if st.button("🚀 Start Conversation", key="create_new_ticket"):
        if subject.strip() and message.strip():
            ticket_id = support_system.create_ticket(user_id, subject, message, priority)
            
            if ticket_id:
                st.session_state.selected_ticket_id = ticket_id
                
                # Clear quick action state
                if 'quick_subject' in st.session_state:
                    del st.session_state.quick_subject
                if 'quick_message' in st.session_state:
                    del st.session_state.quick_message
                
                st.success("✅ Conversation started! An admin will respond soon.")
                st.rerun()
            else:
                st.error("Failed to create support ticket")
        else:
            st.error("Please fill in both subject and message")
