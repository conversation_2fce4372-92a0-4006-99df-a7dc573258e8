"""
AI-Powered Job Matching System
==============================

This module uses Google's free LLM API and RAG to match jobs with user CVs.
"""

import os
import json
import re
from typing import List, Dict, Optional, Tuple
import streamlit as st
import pandas as pd
from datetime import datetime
import hashlib

# Google AI imports
try:
    import google.generativeai as genai
    GOOGLE_AI_AVAILABLE = True
except ImportError:
    GOOGLE_AI_AVAILABLE = False
    st.warning("Google AI not available. Install with: pip install google-generativeai")

# Document processing imports
try:
    import PyPDF2
    from docx import Document
    DOCUMENT_PROCESSING_AVAILABLE = True
except ImportError:
    DOCUMENT_PROCESSING_AVAILABLE = False

# Vector embeddings for RAG
try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False

class JobAIMatcher:
    """AI-powered job matching system using Google's free LLM and RAG"""
    
    def __init__(self):
        self.model = None
        self.embedding_model = None
        self.cv_embeddings = {}
        self.job_embeddings = {}
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize Google AI and embedding models"""
        
        # Initialize Google AI
        if GOOGLE_AI_AVAILABLE:
            api_key = os.getenv('GOOGLE_AI_API_KEY')
            if not api_key:
                # Try to get from Streamlit secrets
                try:
                    api_key = st.secrets.get('GOOGLE_AI_API_KEY')
                except:
                    pass
            
            if api_key:
                try:
                    genai.configure(api_key=api_key)
                    self.model = genai.GenerativeModel('gemini-pro')
                    print("✅ Google AI initialized successfully")
                except Exception as e:
                    print(f"Failed to initialize Google AI: {e}")
            else:
                print("⚠️ Google AI API key not found")
        
        # Initialize embedding model for RAG
        if EMBEDDINGS_AVAILABLE:
            try:
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                print("✅ Embedding model initialized successfully")
            except Exception as e:
                print(f"Failed to initialize embedding model: {e}")
    
    def extract_text_from_file(self, uploaded_file) -> str:
        """Extract text from uploaded CV file"""
        
        if not DOCUMENT_PROCESSING_AVAILABLE:
            return "Document processing not available. Please install PyPDF2 and python-docx."
        
        try:
            file_extension = uploaded_file.name.lower().split('.')[-1]
            
            if file_extension == 'pdf':
                return self._extract_from_pdf(uploaded_file)
            elif file_extension in ['docx', 'doc']:
                return self._extract_from_docx(uploaded_file)
            elif file_extension == 'txt':
                return str(uploaded_file.read(), "utf-8")
            else:
                return "Unsupported file format. Please upload PDF, DOCX, or TXT files."
                
        except Exception as e:
            return f"Error extracting text: {str(e)}"
    
    def _extract_from_pdf(self, uploaded_file) -> str:
        """Extract text from PDF file"""
        try:
            pdf_reader = PyPDF2.PdfReader(uploaded_file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
        except Exception as e:
            return f"Error reading PDF: {str(e)}"
    
    def _extract_from_docx(self, uploaded_file) -> str:
        """Extract text from DOCX file"""
        try:
            doc = Document(uploaded_file)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            return f"Error reading DOCX: {str(e)}"
    
    def analyze_cv_with_ai(self, cv_text: str) -> Dict:
        """Analyze CV using Google AI to extract key information"""
        
        if not self.model:
            return {"error": "Google AI not available"}
        
        prompt = f"""
        Analyze the following CV and extract key information in JSON format:
        
        CV Text:
        {cv_text}
        
        Please extract and return a JSON object with the following fields:
        - skills: List of technical and soft skills
        - experience_years: Estimated years of experience
        - education: Education background
        - job_titles: Previous job titles/roles
        - industries: Industries worked in
        - languages: Programming languages or spoken languages
        - certifications: Any certifications mentioned
        - summary: Brief professional summary
        
        Return only valid JSON, no additional text.
        """
        
        try:
            response = self.model.generate_content(prompt)
            
            # Extract JSON from response
            response_text = response.text.strip()
            
            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # Fallback: try to parse the entire response
                return json.loads(response_text)
                
        except Exception as e:
            print(f"Error analyzing CV with AI: {e}")
            return {"error": str(e)}
    
    def create_embeddings(self, text: str) -> Optional[np.ndarray]:
        """Create embeddings for text using sentence transformers"""
        
        if not self.embedding_model:
            return None
        
        try:
            embeddings = self.embedding_model.encode([text])
            return embeddings[0]
        except Exception as e:
            print(f"Error creating embeddings: {e}")
            return None
    
    def calculate_job_match_score(self, cv_analysis: Dict, job: Dict) -> Tuple[float, Dict]:
        """Calculate match score between CV and job using AI and embeddings"""
        
        if not self.model:
            return 0.0, {"error": "AI not available"}
        
        # Prepare job text for analysis
        job_text = f"""
        Job Title: {job.get('title', '')}
        Company: {job.get('company', '')}
        Location: {job.get('location', '')}
        Description: {job.get('description', '')}
        """
        
        # Create embeddings for similarity calculation
        cv_skills = " ".join(cv_analysis.get('skills', []))
        job_description = job.get('description', '')
        
        similarity_score = 0.0
        if self.embedding_model and cv_skills and job_description:
            try:
                cv_embedding = self.create_embeddings(cv_skills)
                job_embedding = self.create_embeddings(job_description)
                
                if cv_embedding is not None and job_embedding is not None:
                    similarity_score = cosine_similarity(
                        cv_embedding.reshape(1, -1),
                        job_embedding.reshape(1, -1)
                    )[0][0]
            except Exception as e:
                print(f"Error calculating similarity: {e}")
        
        # Use AI to analyze match
        prompt = f"""
        Analyze the match between this CV profile and job posting. Rate the match from 0-100 and provide reasoning.
        
        CV Profile:
        - Skills: {cv_analysis.get('skills', [])}
        - Experience: {cv_analysis.get('experience_years', 'Unknown')} years
        - Previous roles: {cv_analysis.get('job_titles', [])}
        - Industries: {cv_analysis.get('industries', [])}
        
        Job Posting:
        {job_text}
        
        Provide response in JSON format:
        {{
            "match_score": <0-100>,
            "reasoning": "<explanation>",
            "matching_skills": ["skill1", "skill2"],
            "missing_skills": ["skill1", "skill2"],
            "recommendations": "<advice for candidate>"
        }}
        
        Return only valid JSON.
        """
        
        try:
            response = self.model.generate_content(prompt)
            response_text = response.text.strip()
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                ai_analysis = json.loads(json_str)
                
                # Combine AI score with embedding similarity
                ai_score = ai_analysis.get('match_score', 0) / 100.0
                combined_score = (ai_score * 0.7) + (similarity_score * 0.3)
                
                ai_analysis['combined_score'] = combined_score
                ai_analysis['similarity_score'] = similarity_score
                
                return combined_score, ai_analysis
            else:
                return similarity_score, {"error": "Could not parse AI response"}
                
        except Exception as e:
            print(f"Error in AI job matching: {e}")
            return similarity_score, {"error": str(e)}
    
    def rank_jobs_for_cv(self, cv_analysis: Dict, jobs: List[Dict]) -> List[Dict]:
        """Rank jobs based on CV match using AI and RAG"""
        
        ranked_jobs = []
        
        for job in jobs:
            try:
                match_score, match_analysis = self.calculate_job_match_score(cv_analysis, job)
                
                job_with_score = job.copy()
                job_with_score['match_score'] = match_score
                job_with_score['match_analysis'] = match_analysis
                
                ranked_jobs.append(job_with_score)
                
            except Exception as e:
                print(f"Error ranking job {job.get('title', 'Unknown')}: {e}")
                continue
        
        # Sort by match score (highest first)
        ranked_jobs.sort(key=lambda x: x.get('match_score', 0), reverse=True)
        
        return ranked_jobs
    
    def generate_application_advice(self, cv_analysis: Dict, job: Dict) -> str:
        """Generate personalized application advice using AI"""
        
        if not self.model:
            return "AI advice not available"
        
        prompt = f"""
        Generate personalized advice for applying to this job based on the candidate's CV.
        
        Candidate Profile:
        - Skills: {cv_analysis.get('skills', [])}
        - Experience: {cv_analysis.get('experience_years', 'Unknown')} years
        - Background: {cv_analysis.get('summary', '')}
        
        Job:
        - Title: {job.get('title', '')}
        - Company: {job.get('company', '')}
        - Description: {job.get('description', '')}
        
        Provide specific, actionable advice for:
        1. How to tailor the CV for this role
        2. Key points to highlight in cover letter
        3. Skills to emphasize
        4. Potential interview preparation tips
        
        Keep advice concise and practical.
        """
        
        try:
            response = self.model.generate_content(prompt)
            return response.text.strip()
        except Exception as e:
            return f"Error generating advice: {str(e)}"

# Global AI matcher instance
ai_matcher = JobAIMatcher()

def setup_google_ai_key():
    """Setup Google AI API key"""
    st.subheader("🔑 Google AI Setup")
    
    current_key = os.getenv('GOOGLE_AI_API_KEY')
    if current_key:
        st.success("✅ Google AI API key is configured")
        return True
    
    st.info("To use AI job matching, you need a free Google AI API key:")
    st.markdown("""
    1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
    2. Create a free API key
    3. Enter it below
    """)
    
    api_key = st.text_input("Google AI API Key:", type="password", key="google_ai_key")
    
    if api_key:
        os.environ['GOOGLE_AI_API_KEY'] = api_key
        try:
            genai.configure(api_key=api_key)
            ai_matcher.model = genai.GenerativeModel('gemini-pro')
            st.success("✅ Google AI configured successfully!")
            return True
        except Exception as e:
            st.error(f"❌ Invalid API key: {e}")
            return False
    
    return False

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    if not GOOGLE_AI_AVAILABLE:
        missing_deps.append("google-generativeai")
    
    if not DOCUMENT_PROCESSING_AVAILABLE:
        missing_deps.append("PyPDF2 and python-docx")
    
    if not EMBEDDINGS_AVAILABLE:
        missing_deps.append("sentence-transformers and scikit-learn")
    
    if missing_deps:
        st.warning(f"Missing dependencies: {', '.join(missing_deps)}")
        st.info("Install with: pip install google-generativeai PyPDF2 python-docx sentence-transformers scikit-learn")
        return False
    
    return True
